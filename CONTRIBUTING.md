# Contributing to X Terminal

Thank you for your interest in contributing to X Terminal! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

Before creating an issue, please:

1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** provided
3. **Provide detailed information** including:
   - Operating system and version
   - Rust version
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots or logs if applicable

### Suggesting Features

We welcome feature suggestions! Please:

1. **Check the roadmap** to see if it's already planned
2. **Open a discussion** before implementing large features
3. **Provide use cases** and explain the benefit
4. **Consider backwards compatibility**

### Pull Requests

1. **Fork the repository** and create a feature branch
2. **Follow the coding standards** (see below)
3. **Write tests** for new functionality
4. **Update documentation** as needed
5. **Ensure CI passes** before requesting review

## 🛠 Development Setup

### Prerequisites

- Rust 1.70+ with `rustfmt` and `clippy`
- Git
- A modern GPU with Vulkan/DirectX support (for testing rendering)

### Local Development

```bash
# Clone your fork
git clone https://github.com/YOUR_USERNAME/x-terminal.git
cd x-terminal

# Install dependencies
cargo build

# Run tests
cargo test

# Run with debug logging
RUST_LOG=debug cargo run

# Format code
cargo fmt

# Run linter
cargo clippy
```

### Project Structure

```
src/
├── main.rs              # Application entry point
├── lib.rs               # Library root
├── config/              # Configuration management
├── core/                # Core terminal functionality
│   ├── terminal.rs      # Terminal emulation
│   ├── shell.rs         # Shell integration
│   ├── pty.rs           # Pseudo-terminal
│   └── window.rs        # Window management
├── ui/                  # User interface
│   ├── mod.rs           # UI manager
│   ├── components/      # UI components
│   └── events.rs        # Event handling
├── rendering/           # Graphics and rendering
│   ├── mod.rs           # Rendering manager
│   ├── gpu.rs           # GPU context
│   ├── text.rs          # Text rendering
│   └── ui.rs            # UI rendering
├── editor/              # Text editor
│   ├── mod.rs           # Editor core
│   ├── buffer.rs        # Text buffer
│   ├── completion.rs    # Auto-completion
│   └── syntax.rs        # Syntax highlighting
├── ai/                  # AI integration
│   ├── mod.rs           # AI manager
│   ├── providers.rs     # LLM providers
│   ├── agents.rs        # AI agents
│   ├── suggestions.rs   # Command suggestions
│   └── voice.rs         # Voice interface
├── collaboration/       # Collaboration features
├── themes/              # Theme system
├── tools/               # Advanced tools
│   ├── mod.rs           # Tools manager
│   ├── file_ops.rs      # File operations
│   ├── search_ops.rs    # Search operations
│   ├── terminal_ops.rs  # Terminal operations
│   ├── project_ops.rs   # Project management
│   └── package_ops.rs   # Package management
├── performance.rs       # Performance monitoring
└── plugins/             # Plugin system
```

## 📝 Coding Standards

### Rust Style Guide

We follow the official Rust style guide with these additions:

1. **Use `rustfmt`** for consistent formatting
2. **Run `clippy`** and fix all warnings
3. **Write documentation** for public APIs
4. **Use meaningful names** for variables and functions
5. **Prefer explicit types** when it improves clarity

### Code Organization

```rust
// Good: Clear module organization
pub mod terminal {
    pub mod emulation;
    pub mod shell;
}

// Good: Descriptive function names
pub async fn execute_command_with_timeout(
    command: &str, 
    timeout: Duration
) -> Result<CommandOutput> {
    // Implementation
}

// Good: Comprehensive error handling
pub enum TerminalError {
    ShellNotFound(String),
    CommandFailed { exit_code: i32, stderr: String },
    IoError(std::io::Error),
}
```

### Documentation

- **Public APIs** must have documentation comments
- **Complex algorithms** should have inline comments
- **Examples** should be provided for non-trivial functions
- **Update README** when adding major features

```rust
/// Executes a command in the terminal with optional timeout.
/// 
/// # Arguments
/// 
/// * `command` - The command to execute
/// * `timeout` - Maximum execution time
/// 
/// # Returns
/// 
/// Returns `CommandOutput` on success or `TerminalError` on failure.
/// 
/// # Examples
/// 
/// ```rust
/// let output = execute_command_with_timeout("ls -la", Duration::from_secs(5)).await?;
/// println!("Output: {}", output.stdout);
/// ```
pub async fn execute_command_with_timeout(
    command: &str,
    timeout: Duration,
) -> Result<CommandOutput, TerminalError> {
    // Implementation
}
```

### Testing

- **Unit tests** for individual functions
- **Integration tests** for component interactions
- **Property-based tests** for complex logic
- **Performance tests** for critical paths

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_command_execution() {
        let output = execute_command_with_timeout("echo hello", Duration::from_secs(1)).await;
        assert!(output.is_ok());
        assert_eq!(output.unwrap().stdout.trim(), "hello");
    }
    
    #[test]
    fn test_error_handling() {
        // Test error conditions
    }
}
```

## 🏗 Architecture Guidelines

### Async/Await

- Use `async/await` for I/O operations
- Prefer `tokio` for async runtime
- Use `Arc<RwLock<T>>` for shared state
- Avoid blocking operations in async contexts

### Error Handling

- Use `Result<T, E>` for fallible operations
- Create custom error types with `thiserror`
- Provide meaningful error messages
- Log errors appropriately

### Performance

- Profile before optimizing
- Use `cargo bench` for benchmarks
- Prefer zero-copy operations
- Cache expensive computations

### Security

- Validate all user inputs
- Use secure defaults
- Encrypt sensitive data
- Follow principle of least privilege

## 🧪 Testing Guidelines

### Test Categories

1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test component interactions
3. **End-to-End Tests**: Test complete user workflows
4. **Performance Tests**: Measure and validate performance

### Running Tests

```bash
# Run all tests
cargo test

# Run specific test
cargo test test_command_execution

# Run with output
cargo test -- --nocapture

# Run benchmarks
cargo bench

# Test with coverage
cargo tarpaulin --out Html
```

### Test Organization

```rust
// tests/integration_test.rs
use x_terminal::core::Terminal;

#[tokio::test]
async fn test_terminal_startup() {
    let terminal = Terminal::new().await.unwrap();
    assert!(terminal.is_ready());
}
```

## 📋 Pull Request Process

### Before Submitting

1. **Rebase** your branch on the latest main
2. **Run tests** and ensure they pass
3. **Update documentation** if needed
4. **Add changelog entry** for user-facing changes

### PR Description Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing performed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

### Review Process

1. **Automated checks** must pass (CI, tests, linting)
2. **Code review** by maintainers
3. **Testing** on multiple platforms if needed
4. **Approval** from at least one maintainer

## 🎯 Areas for Contribution

### High Priority

- **Performance optimizations** in rendering pipeline
- **Additional AI providers** (Cohere, Hugging Face, etc.)
- **Plugin system** improvements
- **Accessibility** features
- **Mobile support** (Android/iOS)

### Medium Priority

- **Additional themes** and customization options
- **More shell integrations** (Nushell, Xonsh, etc.)
- **Advanced collaboration** features
- **Internationalization** (i18n)
- **Package manager** integrations

### Good First Issues

- **Documentation** improvements
- **Bug fixes** with clear reproduction steps
- **Small feature** additions
- **Test coverage** improvements
- **Code cleanup** and refactoring

## 📞 Getting Help

- **GitHub Discussions**: For questions and general discussion
- **Discord**: Real-time chat with the community
- **Issues**: For bug reports and feature requests
- **Email**: <EMAIL> for private matters

## 🏆 Recognition

Contributors are recognized in:

- **README.md** contributors section
- **Release notes** for significant contributions
- **Hall of Fame** on our website
- **Contributor badges** on Discord

Thank you for contributing to X Terminal! 🚀
