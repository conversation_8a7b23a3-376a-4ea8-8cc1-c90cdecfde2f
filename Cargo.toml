[package]
name = "x-terminal"
version = "0.1.0"
edition = "2021"
authors = ["X Terminal Team"]
description = "A high-performance terminal with enhanced AI capabilities - Warp clone with advanced features"
license = "MIT"
repository = "https://github.com/x-terminal/x-terminal"
keywords = ["terminal", "ai", "warp", "rust", "gpu"]
categories = ["command-line-utilities", "development-tools"]

[dependencies]
# Core async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"
pollster = "0.3"

# GPU rendering and graphics
wgpu = { version = "0.20", features = ["wgsl"] }
winit = "0.30"
cosmic-text = "0.12"
bytemuck = { version = "1.0", features = ["derive"] }

# Terminal and PTY handling
portable-pty = "0.8"
vte = "0.13"
crossterm = "0.28"

# Serialization and configuration
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
config = "0.14"

# HTTP client for AI APIs
reqwest = { version = "0.12", features = ["json", "stream"] }
tokio-tungstenite = "0.24"
async-trait = "0.1"

# Text processing and editing
ropey = "1.6"
tree-sitter = "0.22"
unicode-width = "0.1"
unicode-segmentation = "1.10"
regex = "1.10"

# Logging and error handling
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
dirs = "5.0"
clap = { version = "4.0", features = ["derive"] }
once_cell = "1.19"
parking_lot = "0.12"

# Encryption and security
ring = "0.17"
base64 = "0.22"
glob = "0.3.2"

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_System_Console",
    "Win32_UI_WindowsAndMessaging",
] }

[target.'cfg(unix)'.dependencies]
nix = "0.29"
libc = "0.2"

[dev-dependencies]
criterion = "0.5"
proptest = "1.0"
tempfile = "3.0"

# Benchmarks will be added later
# [[bench]]
# name = "terminal_performance"
# harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 1
