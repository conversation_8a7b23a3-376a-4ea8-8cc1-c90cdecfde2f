# X Terminal 🚀

A modern, AI-powered terminal emulator inspired by Warp, built with Rust and enhanced with multi-LLM AI capabilities.

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()

## ✨ Features

### 🎯 Core Terminal Features
- **High-Performance Terminal Emulation**: Built with Rust for maximum performance and safety
- **Block-Based Command Interface**: Organize commands in discrete, manageable blocks
- **Advanced Text Editor**: Full-featured command input with syntax highlighting and auto-completion
- **Shell Integration**: Deep integration with Bash, Zsh, Fish, and PowerShell
- **Cross-Platform Support**: Works on Windows, macOS, and Linux

### 🤖 AI-Powered Capabilities
- **Multi-LLM Support**: Integration with OpenAI GPT-4, Google Gemini, Anthropic Claude, Mistral AI, and DeepSeek
- **Intelligent Command Suggestions**: AI-powered command completion and suggestions
- **Error Diagnosis**: Automatic error analysis and solution suggestions
- **Code Generation**: Generate scripts and commands using natural language
- **Command Explanation**: Get detailed explanations of complex commands

### 🎨 Modern UI/UX
- **GPU-Accelerated Rendering**: Smooth, responsive interface using wgpu
- **Customizable Themes**: Multiple built-in themes with full customization support
- **Responsive Layout**: Adaptive interface that works on any screen size
- **Smooth Animations**: Polished user experience with fluid transitions

### 🗣️ Voice Interface
- **Voice Commands**: Control the terminal using voice commands
- **Speech-to-Text**: Convert speech to commands using advanced STT engines
- **Text-to-Speech**: Audio feedback for command results and AI responses

### 👥 Collaboration Features
- **Session Sharing**: Share terminal sessions with team members in real-time
- **Knowledge Base**: Collaborative knowledge sharing for commands and solutions
- **Team Insights**: Share AI insights and best practices with your team
- **Real-time Collaboration**: Work together on debugging and development tasks

## 🚀 Quick Start

### Prerequisites
- **Rust**: Version 1.70 or higher
- **Git**: For cloning the repository
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **Graphics**: DirectX 11, Vulkan, or Metal support for GPU acceleration
- **Memory**: Minimum 4GB RAM, recommended 8GB+

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/x-terminal.git
cd x-terminal

# Build the project
cargo build --release

# Run the basic example to test functionality
cargo run --example basic_usage

# Run the main application
cargo run --release

# Run tests to verify everything works
cargo test

# Run integration tests
cargo test --test integration_tests
```

### Optional Dependencies

- **AI Features**: Requires API keys for OpenAI, Claude, Mistral, or DeepSeek
- **Voice Interface**: Requires microphone and speakers
- **Collaboration**: Requires network connectivity for real-time features

### Configuration

X Terminal uses a TOML configuration file. On first run, a default configuration will be created:

```toml
[terminal]
shell = "auto"  # auto, bash, zsh, fish, powershell
font_family = "JetBrains Mono"
font_size = 14
theme = "default-dark"

[ai]
enabled = true
default_provider = "openai"
auto_suggestions = true

[ai.providers.openai]
api_key = "your-api-key-here"
model = "gpt-4"

[collaboration]
enabled = false
server_url = "wss://your-collaboration-server.com"

[voice]
enabled = false
stt_provider = "whisper"
tts_provider = "system"
```

## 🎮 Usage

### Basic Commands

```bash
# Run with default settings
x-terminal

# Run with specific shell
x-terminal run --shell bash

# Run in headless mode
x-terminal --headless

# Show configuration
x-terminal config --show

# List available themes
x-terminal theme --list

# Set a theme
x-terminal theme --set cyberpunk
```

### AI Features

- **Command Explanation**: Type a command and press `Ctrl+?` to get an AI explanation
- **Error Help**: When a command fails, press `Ctrl+H` for AI-powered troubleshooting
- **Command Generation**: Press `Ctrl+G` to generate commands from natural language
- **Smart Suggestions**: AI suggestions appear automatically as you type

### Voice Commands

Enable voice interface in settings, then use:
- "Clear screen" - Clear the terminal
- "Explain command" - Get explanation of current command
- "Suggest command" - Get AI command suggestions
- "Go up/down" - Navigate command history

### Collaboration

1. Enable collaboration in settings
2. Create or join a session: `Ctrl+Shift+C`
3. Share session ID with team members
4. Collaborate in real-time!

## 🏗️ Architecture

X Terminal is built with a modular architecture:

```
src/
├── main.rs              # Application entry point
├── lib.rs               # Library exports
├── config/              # Configuration management
├── core/                # Core terminal functionality
├── ui/                  # User interface
├── rendering/           # GPU-accelerated rendering
├── editor/              # Advanced text editor
├── ai/                  # AI integration
├── blocks/              # Block management
├── collaboration/       # Team features
├── themes/             # Theme management
└── plugins/            # Plugin system
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/x-terminal.git
cd x-terminal

# Install development dependencies
cargo install cargo-watch

# Run tests
cargo test

# Run with hot reload during development
cargo watch -x run
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [Warp Terminal](https://www.warp.dev/)
- Built with [Rust](https://www.rust-lang.org/)
- UI powered by [wgpu](https://wgpu.rs/)
- Text editing with [ropey](https://github.com/cessen/ropey)
- AI integration with multiple LLM providers

## 📞 Support

- 🐛 [Issue Tracker](https://github.com/yourusername/x-terminal/issues)
- 💬 [Discussions](https://github.com/yourusername/x-terminal/discussions)

---

**X Terminal** - Empowering developers with AI-enhanced terminal experiences 🚀
