# X Terminal Fish Integration Hooks
# This script sets up shell integration for fish to enable block-based command tracking

# Check if we're already integrated
if set -q X_TERMINAL_INTEGRATED
    exit 0
end

set -gx X_TERMINAL_INTEGRATED 1
if not set -q X_TERMINAL_SESSION_ID
    set -gx X_TERMINAL_SESSION_ID (uuidgen 2>/dev/null; or echo "fish-"(random)"-"(date +%s))
end

# Function to send hook events to X Terminal
function __x_terminal_send_hook
    set hook_type $argv[1]
    set data $argv[2]
    
    # Send OSC sequence to terminal
    printf '\033]1337;XTerminalHook=%s;SessionId=%s;Data=%s\007' \
        "$hook_type" "$X_TERMINAL_SESSION_ID" "$data"
end

# Pre-command hook (called before each prompt)
function __x_terminal_precmd --on-event fish_prompt
    set exit_code $status
    set end_time (date +%s%3N)
    
    # Send command completion event
    if set -q __x_terminal_cmd_start_time
        set duration (math $end_time - $__x_terminal_cmd_start_time)
        __x_terminal_send_hook "postcmd" "ExitCode=$exit_code;Duration=$duration;EndTime=$end_time;WorkingDir=$PWD"
        set -e __x_terminal_cmd_start_time
    end
    
    # Send precmd event
    __x_terminal_send_hook "precmd" "ExitCode=$exit_code;WorkingDir=$PWD;Time=$end_time"
end

# Pre-execution hook (called before command execution)
function __x_terminal_preexec --on-event fish_preexec
    set cmd $argv[1]
    set start_time (date +%s%3N)
    
    set -g __x_terminal_cmd_start_time $start_time
    
    # Send preexec event
    __x_terminal_send_hook "preexec" "Command=$cmd;StartTime=$start_time;WorkingDir=$PWD"
end

# Post-execution hook (called after command execution)
function __x_terminal_postexec --on-event fish_postexec
    set exit_code $status
    set end_time (date +%s%3N)
    
    # This is handled in precmd, but we can add additional logic here if needed
end

# Send initialization event
__x_terminal_send_hook "init" "Shell=fish;Version=$FISH_VERSION;PID=%self"

# Function to disable X Terminal integration
function x_terminal_disable
    # Remove event handlers
    functions -e __x_terminal_precmd
    functions -e __x_terminal_preexec
    functions -e __x_terminal_postexec
    
    set -e X_TERMINAL_INTEGRATED
    set -e X_TERMINAL_SESSION_ID
    set -e __x_terminal_cmd_start_time
    
    # Remove functions
    functions -e __x_terminal_send_hook
    functions -e x_terminal_disable
end

# Function to get X Terminal status
function x_terminal_status
    echo "X Terminal Integration: "(set -q X_TERMINAL_INTEGRATED; and echo "enabled"; or echo "disabled")
    echo "Session ID: "(set -q X_TERMINAL_SESSION_ID; and echo $X_TERMINAL_SESSION_ID; or echo "none")
    echo "Shell: fish $FISH_VERSION"
    echo "PID: %self"
end

echo "X Terminal fish integration loaded (Session: $X_TERMINAL_SESSION_ID)"
