# X Terminal PowerShell Integration Hooks
# This script sets up shell integration for PowerShell to enable block-based command tracking

# Check if we're already integrated
if ($env:X_TERMINAL_INTEGRATED) {
    return
}

$env:X_TERMINAL_INTEGRATED = "1"
if (-not $env:X_TERMINAL_SESSION_ID) {
    $env:X_TERMINAL_SESSION_ID = "pwsh-$PID-$(Get-Date -UFormat %s)"
}

# Function to send hook events to X Terminal
function Send-XTerminalHook {
    param(
        [string]$HookType,
        [string]$Data
    )
    
    # Send OSC sequence to terminal
    $sequence = "`e]1337;XTerminalHook=$HookType;SessionId=$($env:X_TERMINAL_SESSION_ID);Data=$Data`a"
    Write-Host $sequence -NoNewline
}

# Store original prompt function
if (Get-Command prompt -ErrorAction SilentlyContinue) {
    $global:OriginalPrompt = Get-Command prompt
}

# Custom prompt function with hooks
function prompt {
    $exitCode = $LASTEXITCODE
    $endTime = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
    
    # Send command completion event
    if ($global:XTerminalCmdStartTime) {
        $duration = $endTime - $global:XTerminalCmdStartTime
        Send-XTerminalHook "postcmd" "ExitCode=$exitCode;Duration=$duration;EndTime=$endTime;WorkingDir=$PWD"
        Remove-Variable -Name XTerminalCmdStartTime -Scope Global -ErrorAction SilentlyContinue
    }
    
    # Send precmd event
    Send-XTerminalHook "precmd" "ExitCode=$exitCode;WorkingDir=$PWD;Time=$endTime"
    
    # Call original prompt or default
    if ($global:OriginalPrompt) {
        & $global:OriginalPrompt.ScriptBlock
    } else {
        "PS $($executionContext.SessionState.Path.CurrentLocation)$('>' * ($nestedPromptLevel + 1)) "
    }
}

# PowerShell doesn't have a direct preexec equivalent, so we use command history
# This is a simplified approach - in a full implementation, we might use PSReadLine events
$global:XTerminalLastHistoryId = (Get-History | Select-Object -Last 1).Id

# Function to check for new commands (called from prompt)
function Test-XTerminalNewCommand {
    $currentHistoryId = (Get-History | Select-Object -Last 1).Id
    
    if ($currentHistoryId -gt $global:XTerminalLastHistoryId) {
        $lastCommand = Get-History | Select-Object -Last 1
        $startTime = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
        
        $global:XTerminalCmdStartTime = $startTime
        $global:XTerminalLastHistoryId = $currentHistoryId
        
        # Send preexec event
        Send-XTerminalHook "preexec" "Command=$($lastCommand.CommandLine);StartTime=$startTime;WorkingDir=$PWD"
    }
}

# Send initialization event
Send-XTerminalHook "init" "Shell=powershell;Version=$($PSVersionTable.PSVersion);PID=$PID"

# Function to disable X Terminal integration
function Disable-XTerminalIntegration {
    # Restore original prompt
    if ($global:OriginalPrompt) {
        Set-Item -Path function:prompt -Value $global:OriginalPrompt.ScriptBlock
    }
    
    Remove-Variable -Name env:X_TERMINAL_INTEGRATED -ErrorAction SilentlyContinue
    Remove-Variable -Name env:X_TERMINAL_SESSION_ID -ErrorAction SilentlyContinue
    Remove-Variable -Name XTerminalCmdStartTime -Scope Global -ErrorAction SilentlyContinue
    Remove-Variable -Name XTerminalLastHistoryId -Scope Global -ErrorAction SilentlyContinue
    Remove-Variable -Name OriginalPrompt -Scope Global -ErrorAction SilentlyContinue
    
    # Remove functions
    Remove-Item -Path function:Send-XTerminalHook -ErrorAction SilentlyContinue
    Remove-Item -Path function:Test-XTerminalNewCommand -ErrorAction SilentlyContinue
    Remove-Item -Path function:Disable-XTerminalIntegration -ErrorAction SilentlyContinue
    Remove-Item -Path function:Get-XTerminalStatus -ErrorAction SilentlyContinue
}

# Function to get X Terminal status
function Get-XTerminalStatus {
    Write-Host "X Terminal Integration: $($env:X_TERMINAL_INTEGRATED -eq '1' ? 'enabled' : 'disabled')"
    Write-Host "Session ID: $($env:X_TERMINAL_SESSION_ID ?? 'none')"
    Write-Host "Shell: PowerShell $($PSVersionTable.PSVersion)"
    Write-Host "PID: $PID"
}

Write-Host "X Terminal PowerShell integration loaded (Session: $($env:X_TERMINAL_SESSION_ID))"
