//! AI integration and management
//! 
//! This module provides AI capabilities including multiple LLM provider support,
//! command suggestions, code generation, and autonomous coding assistance.

pub mod providers;
pub mod agents;
pub mod suggestions;
pub mod voice;

use crate::config::ConfigManager;
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;

/// AI manager for coordinating all AI features
pub struct AIManager {
    /// Configuration
    config: Arc<ConfigManager>,
    /// LLM providers
    providers: Arc<RwLock<providers::ProviderManager>>,
    /// AI agents
    agents: Arc<RwLock<agents::AgentManager>>,
    /// Suggestion engine
    suggestions: Arc<RwLock<suggestions::SuggestionEngine>>,
    /// Voice interface
    voice: Arc<RwLock<voice::VoiceInterface>>,
}

impl AIManager {
    /// Create a new AI manager
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let providers = Arc::new(RwLock::new(providers::ProviderManager::new(config).await?));
        let agents = Arc::new(RwLock::new(agents::AgentManager::new(config).await?));
        let suggestions = Arc::new(RwLock::new(suggestions::SuggestionEngine::new(config).await?));
        let voice = Arc::new(RwLock::new(voice::VoiceInterface::new(config).await?));
        
        Ok(Self {
            config: config.clone(),
            providers,
            agents,
            suggestions,
            voice,
        })
    }
    
    /// Initialize AI system
    pub async fn initialize(&self) -> Result<()> {
        tracing::info!("Initializing AI system...");
        
        // Initialize providers
        {
            let mut providers = self.providers.write().await;
            providers.initialize().await?;
        }
        
        // Initialize agents
        {
            let mut agents = self.agents.write().await;
            agents.initialize().await?;
        }
        
        // Initialize suggestion engine
        {
            let mut suggestions = self.suggestions.write().await;
            suggestions.initialize().await?;
        }
        
        // Initialize voice interface
        {
            let mut voice = self.voice.write().await;
            voice.initialize().await?;
        }
        
        tracing::info!("AI system initialized successfully");
        Ok(())
    }
    
    /// Process pending AI requests
    pub async fn process_pending_requests(&self) -> Result<()> {
        // Process agent requests
        {
            let mut agents = self.agents.write().await;
            agents.process_pending().await?;
        }
        
        // Process suggestion requests
        {
            let mut suggestions = self.suggestions.write().await;
            suggestions.process_pending().await?;
        }
        
        Ok(())
    }
    
    /// Get command suggestions
    pub async fn get_command_suggestions(&self, context: &str) -> Result<Vec<String>> {
        let suggestions = self.suggestions.read().await;
        suggestions.get_command_suggestions(context).await
    }
    
    /// Generate code
    pub async fn generate_code(&self, prompt: &str, language: &str) -> Result<String> {
        let agents = self.agents.read().await;
        agents.generate_code(prompt, language).await
    }
    
    /// Analyze error and provide suggestions
    pub async fn analyze_error(&self, error_output: &str, context: &str) -> Result<Vec<String>> {
        let agents = self.agents.read().await;
        agents.analyze_error(error_output, context).await
    }
}

/// AI-related errors
#[derive(thiserror::Error, Debug)]
pub enum AIError {
    #[error("Provider error: {0}")]
    Provider(#[from] providers::ProviderError),
    
    #[error("Agent error: {0}")]
    Agent(#[from] agents::AgentError),
    
    #[error("Suggestion error: {0}")]
    Suggestion(String),
    
    #[error("Voice error: {0}")]
    Voice(String),
    
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("Other error: {0}")]
    Other(#[from] anyhow::Error),
}
