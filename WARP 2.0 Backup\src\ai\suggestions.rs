//! Command suggestion engine
//! 
//! This module implements intelligent command suggestions based on context,
//! history, and AI analysis.

use crate::config::ConfigManager;
use super::providers::{ProviderManager, CompletionRequest, Message};
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Suggestion engine for command completions and suggestions
pub struct SuggestionEngine {
    config: Arc<ConfigManager>,
    providers: Arc<RwLock<ProviderManager>>,
    command_history: Vec<String>,
    context_cache: std::collections::HashMap<String, Vec<String>>,
}

impl SuggestionEngine {
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let providers = Arc::new(RwLock::new(ProviderManager::new(config).await?));
        
        Ok(Self {
            config: config.clone(),
            providers,
            command_history: Vec::new(),
            context_cache: std::collections::HashMap::new(),
        })
    }
    
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Initializing suggestion engine...");
        
        // Load command history
        self.load_command_history().await?;
        
        tracing::info!("Suggestion engine initialized successfully");
        Ok(())
    }
    
    pub async fn process_pending(&mut self) -> Result<()> {
        // Process any pending suggestion requests
        Ok(())
    }
    
    pub async fn get_command_suggestions(&self, context: &str) -> Result<Vec<String>> {
        // Check cache first
        if let Some(cached) = self.context_cache.get(context) {
            return Ok(cached.clone());
        }
        
        let request = CompletionRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![
                Message {
                    role: "system".to_string(),
                    content: "You are a command-line assistant. Suggest relevant shell commands based on the user's context. Return suggestions as a JSON array of strings.".to_string(),
                },
                Message {
                    role: "user".to_string(),
                    content: format!("Context: {}\nRecent commands: {:?}", context, self.command_history.iter().rev().take(5).collect::<Vec<_>>()),
                },
            ],
            max_tokens: Some(500),
            temperature: Some(0.3),
            stream: false,
        };
        
        let providers = self.providers.read().await;
        let response = providers.complete("openai", &request).await?;
        
        // Try to parse as JSON array, fallback to single suggestion
        let suggestions = match serde_json::from_str::<Vec<String>>(&response.content) {
            Ok(suggestions) => suggestions,
            Err(_) => vec![response.content],
        };
        
        Ok(suggestions)
    }
    
    async fn load_command_history(&mut self) -> Result<()> {
        // TODO: Load command history from file or database
        Ok(())
    }
    
    pub fn add_command_to_history(&mut self, command: String) {
        self.command_history.push(command);
        
        // Keep only recent commands
        if self.command_history.len() > 1000 {
            self.command_history.drain(0..100);
        }
    }
}
