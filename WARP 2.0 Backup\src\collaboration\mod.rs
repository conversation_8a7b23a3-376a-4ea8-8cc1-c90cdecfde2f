//! Collaboration and team features
//! 
//! This module implements team collaboration, session sharing, and knowledge management.

use crate::config::ConfigManager;
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Collaboration manager for team features
pub struct CollaborationManager {
    config: Arc<ConfigManager>,
    active_sessions: Arc<RwLock<HashMap<Uuid, CollaborationSession>>>,
    shared_knowledge: Arc<RwLock<KnowledgeBase>>,
}

/// Collaboration session
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollaborationSession {
    pub id: Uuid,
    pub name: String,
    pub participants: Vec<Participant>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub session_type: SessionType,
}

/// Session participant
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Participant {
    pub id: Uuid,
    pub username: String,
    pub role: ParticipantRole,
    pub joined_at: chrono::DateTime<chrono::Utc>,
}

/// Participant roles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ParticipantRole {
    Owner,
    Admin,
    Member,
    Viewer,
}

/// Session types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionType {
    TerminalShare,
    CommandReview,
    KnowledgeShare,
    Debugging,
}

/// Knowledge base for shared insights
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeBase {
    pub entries: HashMap<Uuid, KnowledgeEntry>,
    pub tags: HashMap<String, Vec<Uuid>>,
    pub categories: HashMap<String, Vec<Uuid>>,
}

/// Knowledge entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeEntry {
    pub id: Uuid,
    pub title: String,
    pub content: String,
    pub author: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub tags: Vec<String>,
    pub category: String,
    pub votes: i32,
    pub entry_type: KnowledgeEntryType,
}

/// Types of knowledge entries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KnowledgeEntryType {
    Command,
    Script,
    Troubleshooting,
    BestPractice,
    Tutorial,
}

impl CollaborationManager {
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let active_sessions = Arc::new(RwLock::new(HashMap::new()));
        let shared_knowledge = Arc::new(RwLock::new(KnowledgeBase {
            entries: HashMap::new(),
            tags: HashMap::new(),
            categories: HashMap::new(),
        }));

        Ok(Self {
            config: config.clone(),
            active_sessions,
            shared_knowledge,
        })
    }

    pub async fn initialize(&self) -> Result<()> {
        tracing::info!("Collaboration manager initialized");
        // TODO: Load existing knowledge base
        // TODO: Connect to collaboration server
        Ok(())
    }

    /// Create a new collaboration session
    pub async fn create_session(&self, name: String, session_type: SessionType, owner: Participant) -> Result<Uuid> {
        let session_id = Uuid::new_v4();
        let session = CollaborationSession {
            id: session_id,
            name,
            participants: vec![owner],
            created_at: chrono::Utc::now(),
            last_activity: chrono::Utc::now(),
            session_type,
        };

        let mut sessions = self.active_sessions.write().await;
        sessions.insert(session_id, session);

        tracing::info!("Created collaboration session: {}", session_id);
        Ok(session_id)
    }

    /// Join a collaboration session
    pub async fn join_session(&self, session_id: Uuid, participant: Participant) -> Result<()> {
        let mut sessions = self.active_sessions.write().await;

        if let Some(session) = sessions.get_mut(&session_id) {
            session.participants.push(participant);
            session.last_activity = chrono::Utc::now();
            tracing::info!("Participant joined session: {}", session_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Session not found: {}", session_id))
        }
    }

    /// Add knowledge entry
    pub async fn add_knowledge_entry(&self, entry: KnowledgeEntry) -> Result<()> {
        let mut knowledge = self.shared_knowledge.write().await;

        // Add to entries
        knowledge.entries.insert(entry.id, entry.clone());

        // Update tags index
        for tag in &entry.tags {
            knowledge.tags.entry(tag.clone()).or_insert_with(Vec::new).push(entry.id);
        }

        // Update category index
        knowledge.categories.entry(entry.category.clone()).or_insert_with(Vec::new).push(entry.id);

        tracing::info!("Added knowledge entry: {}", entry.title);
        Ok(())
    }

    /// Search knowledge base
    pub async fn search_knowledge(&self, query: &str) -> Result<Vec<KnowledgeEntry>> {
        let knowledge = self.shared_knowledge.read().await;
        let mut results = Vec::new();

        for entry in knowledge.entries.values() {
            if entry.title.to_lowercase().contains(&query.to_lowercase()) ||
               entry.content.to_lowercase().contains(&query.to_lowercase()) ||
               entry.tags.iter().any(|tag| tag.to_lowercase().contains(&query.to_lowercase())) {
                results.push(entry.clone());
            }
        }

        // Sort by votes (popularity)
        results.sort_by(|a, b| b.votes.cmp(&a.votes));

        Ok(results)
    }

    /// Get knowledge entries by category
    pub async fn get_knowledge_by_category(&self, category: &str) -> Result<Vec<KnowledgeEntry>> {
        let knowledge = self.shared_knowledge.read().await;
        let mut results = Vec::new();

        if let Some(entry_ids) = knowledge.categories.get(category) {
            for id in entry_ids {
                if let Some(entry) = knowledge.entries.get(id) {
                    results.push(entry.clone());
                }
            }
        }

        Ok(results)
    }

    /// Vote on knowledge entry
    pub async fn vote_knowledge_entry(&self, entry_id: Uuid, upvote: bool) -> Result<()> {
        let mut knowledge = self.shared_knowledge.write().await;

        if let Some(entry) = knowledge.entries.get_mut(&entry_id) {
            if upvote {
                entry.votes += 1;
            } else {
                entry.votes -= 1;
            }
            entry.updated_at = chrono::Utc::now();
            tracing::info!("Voted on knowledge entry: {}", entry_id);
        }

        Ok(())
    }

    /// Get active sessions
    pub async fn get_active_sessions(&self) -> Result<Vec<CollaborationSession>> {
        let sessions = self.active_sessions.read().await;
        Ok(sessions.values().cloned().collect())
    }

    pub async fn process_events(&self) -> Result<()> {
        // TODO: Implement collaboration event processing
        Ok(())
    }

    /// Share a session with other users
    pub async fn share_session(&self, session_id: &Uuid, users: Vec<String>) -> Result<String> {
        tracing::info!("Sharing session {} with users: {:?}", session_id, users);

        // Create participants from user list
        let participants: Vec<Participant> = users.into_iter().map(|username| {
            Participant {
                id: Uuid::new_v4(),
                username,
                role: ParticipantRole::Member,
                joined_at: chrono::Utc::now(),
            }
        }).collect();

        // Create a new shared session
        let shared_session = CollaborationSession {
            id: Uuid::new_v4(),
            name: format!("Shared Session from {}", session_id),
            participants,
            created_at: chrono::Utc::now(),
            last_activity: chrono::Utc::now(),
            session_type: SessionType::TerminalShare,
        };

        // Store the shared session
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.insert(shared_session.id, shared_session.clone());
        }

        tracing::info!("Created shared session: {}", shared_session.id);

        // Notify participants
        for participant in &shared_session.participants {
            self.notify_user_session_shared(&participant.username, &shared_session).await?;
        }

        Ok(shared_session.id.to_string())
    }

    /// Notify user about shared session
    async fn notify_user_session_shared(&self, user: &str, session: &CollaborationSession) -> Result<()> {
        tracing::info!("Notifying user {} about shared session {}", user, session.id);

        // TODO: Send actual notification (email, websocket, etc.)
        // For now, just log the notification

        Ok(())
    }

    /// Join a shared session (enhanced version)
    pub async fn join_session_enhanced(&self, session_id: &Uuid, user: Participant) -> Result<()> {
        let mut sessions = self.active_sessions.write().await;

        if let Some(session) = sessions.get_mut(session_id) {
            // Check if user is already in session
            if !session.participants.iter().any(|p| p.username == user.username) {
                let username = user.username.clone();
                session.participants.push(user);
                session.last_activity = chrono::Utc::now();
                tracing::info!("User {} joined session {}", username, session_id);
            }
            Ok(())
        } else {
            Err(CollaborationError::Network("Session not found".to_string()).into())
        }
    }

    /// Leave a shared session
    pub async fn leave_session(&self, session_id: &Uuid, username: &str) -> Result<()> {
        let mut sessions = self.active_sessions.write().await;

        if let Some(session) = sessions.get_mut(session_id) {
            session.participants.retain(|p| p.username != username);
            session.last_activity = chrono::Utc::now();
            tracing::info!("User {} left session {}", username, session_id);

            // Remove session if no participants left
            if session.participants.is_empty() {
                sessions.remove(session_id);
                tracing::info!("Session {} removed (no participants)", session_id);
            }

            Ok(())
        } else {
            Err(CollaborationError::Network("Session not found".to_string()).into())
        }
    }

    /// Get active sessions (enhanced version)
    pub async fn get_active_sessions_enhanced(&self) -> Result<Vec<CollaborationSession>> {
        let sessions = self.active_sessions.read().await;
        Ok(sessions.values().cloned().collect())
    }

    /// Get session by ID
    pub async fn get_session(&self, session_id: &Uuid) -> Result<Option<CollaborationSession>> {
        let sessions = self.active_sessions.read().await;
        Ok(sessions.get(session_id).cloned())
    }
}

/// Collaboration errors
#[derive(thiserror::Error, Debug)]
pub enum CollaborationError {
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Authentication error: {0}")]
    Auth(String),
    
    #[error("Other error: {0}")]
    Other(#[from] anyhow::Error),
}
