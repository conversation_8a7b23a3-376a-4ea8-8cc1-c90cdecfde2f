//! Configuration management
//! 
//! This module handles all configuration aspects of X Terminal including
//! user preferences, AI settings, themes, and system configuration.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Configuration manager
pub struct ConfigManager {
    /// Configuration file path
    config_path: PathBuf,
    /// Current configuration
    config: XTerminalConfig,
}

/// Main configuration structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct XTerminalConfig {
    /// General settings
    pub general: GeneralConfig,
    /// Terminal settings
    pub terminal: TerminalConfig,
    /// AI settings
    pub ai: AIConfig,
    /// Theme settings
    pub theme: ThemeConfig,
    /// Collaboration settings
    pub collaboration: CollaborationConfig,
}

/// General application settings
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GeneralConfig {
    /// Default shell
    pub default_shell: Option<String>,
    /// Working directory
    pub working_directory: Option<String>,
    /// Enable logging
    pub enable_logging: bool,
    /// Log level
    pub log_level: String,
}

/// Terminal-specific settings
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TerminalConfig {
    /// Terminal size
    pub rows: u16,
    pub cols: u16,
    /// Font settings
    pub font_family: String,
    pub font_size: f32,
    /// Scrollback buffer size
    pub scrollback_lines: usize,
    /// Enable mouse support
    pub enable_mouse: bool,
}

/// AI configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    /// Enable AI features
    pub enabled: bool,
    /// Default AI provider
    pub default_provider: String,
    /// API keys for different providers
    pub api_keys: std::collections::HashMap<String, String>,
    /// AI model preferences
    pub model_preferences: std::collections::HashMap<String, String>,
}

/// Theme configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeConfig {
    /// Current theme name
    pub current_theme: String,
    /// Custom themes
    pub custom_themes: Vec<String>,
}

/// Collaboration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollaborationConfig {
    /// Enable collaboration features
    pub enabled: bool,
    /// User information
    pub user_name: Option<String>,
    pub user_email: Option<String>,
}

impl Default for XTerminalConfig {
    fn default() -> Self {
        Self {
            general: GeneralConfig {
                default_shell: None,
                working_directory: None,
                enable_logging: true,
                log_level: "info".to_string(),
            },
            terminal: TerminalConfig {
                rows: 24,
                cols: 80,
                font_family: "JetBrains Mono".to_string(),
                font_size: 14.0,
                scrollback_lines: 10000,
                enable_mouse: true,
            },
            ai: AIConfig {
                enabled: true,
                default_provider: "openai".to_string(),
                api_keys: std::collections::HashMap::new(),
                model_preferences: std::collections::HashMap::new(),
            },
            theme: ThemeConfig {
                current_theme: "default".to_string(),
                custom_themes: Vec::new(),
            },
            collaboration: CollaborationConfig {
                enabled: true,
                user_name: None,
                user_email: None,
            },
        }
    }
}

impl ConfigManager {
    /// Create a new configuration manager
    pub async fn new() -> Result<Self> {
        let config_path = Self::get_config_path()?;
        let config = Self::load_config(&config_path).await?;
        
        Ok(Self {
            config_path,
            config,
        })
    }
    
    /// Get the configuration file path
    fn get_config_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find config directory"))?;
        
        let x_terminal_dir = config_dir.join("x-terminal");
        std::fs::create_dir_all(&x_terminal_dir)?;
        
        Ok(x_terminal_dir.join("config.toml"))
    }
    
    /// Load configuration from file
    async fn load_config(path: &PathBuf) -> Result<XTerminalConfig> {
        if path.exists() {
            let content = tokio::fs::read_to_string(path).await?;
            let config: XTerminalConfig = toml::from_str(&content)?;
            Ok(config)
        } else {
            // Create default config
            let config = XTerminalConfig::default();
            Self::save_config(path, &config).await?;
            Ok(config)
        }
    }
    
    /// Save configuration to file
    async fn save_config(path: &PathBuf, config: &XTerminalConfig) -> Result<()> {
        let content = toml::to_string_pretty(config)?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }
    
    /// Get current configuration
    pub fn config(&self) -> &XTerminalConfig {
        &self.config
    }
    
    /// Update configuration
    pub async fn update_config<F>(&mut self, updater: F) -> Result<()>
    where
        F: FnOnce(&mut XTerminalConfig),
    {
        updater(&mut self.config);
        Self::save_config(&self.config_path, &self.config).await?;
        Ok(())
    }
    
    /// Get AI API key for provider
    pub fn get_ai_api_key(&self, provider: &str) -> Option<&String> {
        self.config.ai.api_keys.get(provider)
    }
    
    /// Set AI API key for provider
    pub async fn set_ai_api_key(&mut self, provider: String, api_key: String) -> Result<()> {
        self.config.ai.api_keys.insert(provider, api_key);
        Self::save_config(&self.config_path, &self.config).await?;
        Ok(())
    }
}

/// Configuration errors
#[derive(thiserror::Error, Debug)]
pub enum ConfigError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] toml::ser::Error),
    
    #[error("Deserialization error: {0}")]
    Deserialization(#[from] toml::de::Error),
    
    #[error("Configuration error: {0}")]
    Config(String),
}
