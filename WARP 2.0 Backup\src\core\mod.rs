//! Core terminal functionality
//! 
//! This module contains the core terminal engine including VT100 parsing,
//! pseudoterminal handling, and shell integration.

pub mod terminal;
pub mod pty;
pub mod parser;
pub mod shell;
pub mod events;
pub mod window;

pub use terminal::Terminal;
pub use pty::PtyManager;
pub use parser::VT100Parser;
pub use shell::ShellIntegration;
pub use events::{TerminalEvent, EventHandler};

/// Terminal-related errors
#[derive(thiserror::Error, Debug)]
pub enum TerminalError {
    #[error("PTY error: {0}")]
    Pty(#[from] std::io::Error),
    
    #[error("Parser error: {0}")]
    Parser(String),
    
    #[error("Shell integration error: {0}")]
    Shell(String),
    
    #[error("Event handling error: {0}")]
    Event(String),
    
    #[error("Configuration error: {0}")]
    Config(String),
}

pub type TerminalResult<T> = Result<T, TerminalError>;
