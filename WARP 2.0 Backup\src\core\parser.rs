//! VT100/ANSI escape sequence parser
//! 
//! This module implements a VT100-compatible parser for handling ANSI escape sequences
//! and converting them into terminal actions.

use super::TerminalResult;
use super::terminal::{ParserAction, Color, CellAttributes};
use vte::{Parser, Perform};

/// VT100 parser for ANSI escape sequences
pub struct VT100Parser {
    /// VTE parser
    parser: Parser,
    /// Collected actions
    actions: Vec<ParserAction>,
    /// Current parser state
    state: ParserState,
}

/// Parser performer that implements the VTE Perform trait
struct ParserPerformer<'a> {
    actions: &'a mut Vec<ParserAction>,
    state: &'a mut ParserState,
}

/// Parser state for tracking context
#[derive(Debug, Default)]
struct ParserState {
    /// Current foreground color
    fg_color: Color,
    /// Current background color
    bg_color: Color,
    /// Current cell attributes
    attributes: CellAttributes,
    /// Cursor position
    cursor_row: usize,
    cursor_col: usize,
}

impl VT100Parser {
    /// Create a new VT100 parser
    pub fn new() -> Self {
        Self {
            parser: Parser::new(),
            actions: Vec::new(),
            state: ParserState::default(),
        }
    }
    
    /// Parse input data and return actions
    pub fn parse(&mut self, data: &[u8]) -> TerminalResult<Vec<ParserAction>> {
        self.actions.clear();

        for &byte in data {
            let mut performer = ParserPerformer {
                actions: &mut self.actions,
                state: &mut self.state,
            };
            self.parser.advance(&mut performer, byte);
        }

        Ok(std::mem::take(&mut self.actions))
    }
    

}

impl<'a> ParserPerformer<'a> {
    /// Add an action to the list
    fn add_action(&mut self, action: ParserAction) {
        self.actions.push(action);
    }

    /// Handle SGR (Set Graphics Rendition) sequences
    fn handle_sgr_params(&mut self, params: &vte::Params) {
        for param in params.iter() {
            for &value in param {
                match value {
                    0 => {
                        // Reset all attributes
                        self.state.attributes = CellAttributes::default();
                        self.state.fg_color = Color::Default;
                        self.state.bg_color = Color::Default;
                    }
                    1 => self.state.attributes.bold = true,
                    2 => self.state.attributes.dim = true,
                    3 => self.state.attributes.italic = true,
                    4 => self.state.attributes.underline = true,
                    5 => self.state.attributes.blink = true,
                    7 => self.state.attributes.reverse = true,
                    9 => self.state.attributes.strikethrough = true,
                    22 => {
                        self.state.attributes.bold = false;
                        self.state.attributes.dim = false;
                    }
                    23 => self.state.attributes.italic = false,
                    24 => self.state.attributes.underline = false,
                    25 => self.state.attributes.blink = false,
                    27 => self.state.attributes.reverse = false,
                    29 => self.state.attributes.strikethrough = false,
                    30..=37 => {
                        // Foreground colors
                        self.state.fg_color = Color::Named((value - 30) as u8);
                    }
                    38 => {
                        // Extended foreground color (256-color or RGB)
                        // TODO: Handle extended color sequences
                    }
                    39 => {
                        // Default foreground color
                        self.state.fg_color = Color::Default;
                    }
                    40..=47 => {
                        // Background colors
                        self.state.bg_color = Color::Named((value - 40) as u8);
                    }
                    48 => {
                        // Extended background color (256-color or RGB)
                        // TODO: Handle extended color sequences
                    }
                    49 => {
                        // Default background color
                        self.state.bg_color = Color::Default;
                    }
                    90..=97 => {
                        // Bright foreground colors
                        self.state.fg_color = Color::Named((value - 90 + 8) as u8);
                    }
                    100..=107 => {
                        // Bright background colors
                        self.state.bg_color = Color::Named((value - 100 + 8) as u8);
                    }
                    _ => {
                        tracing::debug!("Unhandled SGR parameter: {}", value);
                    }
                }
            }
        }

        // Apply the current colors and attributes
        self.add_action(ParserAction::SetColor(self.state.fg_color, self.state.bg_color));
        self.add_action(ParserAction::SetAttributes(self.state.attributes.clone()));
    }
}

impl<'a> Perform for ParserPerformer<'a> {
    /// Handle printable characters
    fn print(&mut self, c: char) {
        self.add_action(ParserAction::Print(c));
    }
    
    /// Handle control characters
    fn execute(&mut self, byte: u8) {
        match byte {
            b'\n' => {
                // Line feed - move cursor down
                self.state.cursor_row += 1;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\r' => {
                // Carriage return - move cursor to beginning of line
                self.state.cursor_col = 0;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\t' => {
                // Tab - move cursor to next tab stop (every 8 columns)
                self.state.cursor_col = (self.state.cursor_col + 8) & !7;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\x08' => {
                // Backspace - move cursor left
                if self.state.cursor_col > 0 {
                    self.state.cursor_col -= 1;
                    self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
                }
            }
            b'\x07' => {
                // Bell - could trigger notification
                // TODO: Implement bell handling
            }
            _ => {
                // Other control characters - mostly ignored for now
            }
        }
    }
    
    /// Handle escape sequences
    fn csi_dispatch(&mut self, params: &vte::Params, _intermediates: &[u8], ignore: bool, c: char) {
        if ignore {
            return;
        }
        
        match c {
            'A' => {
                // Cursor up
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                if self.state.cursor_row >= n {
                    self.state.cursor_row -= n;
                } else {
                    self.state.cursor_row = 0;
                }
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'B' => {
                // Cursor down
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_row += n;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'C' => {
                // Cursor right
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_col += n;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'D' => {
                // Cursor left
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                if self.state.cursor_col >= n {
                    self.state.cursor_col -= n;
                } else {
                    self.state.cursor_col = 0;
                }
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'H' | 'f' => {
                // Cursor position
                let row = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                let col = params.iter().nth(1).and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_row = row.saturating_sub(1);
                self.state.cursor_col = col.saturating_sub(1);
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'J' => {
                // Erase in display
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(0);
                match n {
                    0 => {
                        // Clear from cursor to end of screen
                        // TODO: Implement partial clear
                    }
                    1 => {
                        // Clear from beginning of screen to cursor
                        // TODO: Implement partial clear
                    }
                    2 => {
                        // Clear entire screen
                        self.add_action(ParserAction::ClearScreen);
                    }
                    _ => {}
                }
            }
            'K' => {
                // Erase in line
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(0);
                match n {
                    0 => {
                        // Clear from cursor to end of line
                        // TODO: Implement partial line clear
                    }
                    1 => {
                        // Clear from beginning of line to cursor
                        // TODO: Implement partial line clear
                    }
                    2 => {
                        // Clear entire line
                        self.add_action(ParserAction::ClearLine);
                    }
                    _ => {}
                }
            }
            'm' => {
                // Set graphics rendition (colors and attributes)
                self.handle_sgr_params(params);
            }
            'S' => {
                // Scroll up
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.add_action(ParserAction::ScrollUp(n));
            }
            'T' => {
                // Scroll down
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.add_action(ParserAction::ScrollDown(n));
            }
            _ => {
                // Unhandled CSI sequence
                tracing::debug!("Unhandled CSI sequence: {} with params {:?}", c, params);
            }
        }
    }

    /// Handle other escape sequences (mostly unimplemented for now)
    fn esc_dispatch(&mut self, intermediates: &[u8], ignore: bool, byte: u8) {
        if ignore {
            return;
        }
        
        match byte {
            b'D' => {
                // Index - move cursor down one line
                self.state.cursor_row += 1;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'M' => {
                // Reverse index - move cursor up one line
                if self.state.cursor_row > 0 {
                    self.state.cursor_row -= 1;
                    self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
                }
            }
            b'c' => {
                // Reset terminal
                *self.state = ParserState::default();
                self.add_action(ParserAction::ClearScreen);
            }
            _ => {
                tracing::debug!("Unhandled ESC sequence: {} with intermediates {:?}", byte as char, intermediates);
            }
        }
    }
    
    /// Handle hook sequences (mostly unimplemented)
    fn hook(&mut self, _params: &vte::Params, _intermediates: &[u8], _ignore: bool, _c: char) {
        // TODO: Implement hook handling if needed
    }

    /// Handle put sequences (mostly unimplemented)
    fn put(&mut self, _byte: u8) {
        // TODO: Implement put handling if needed
    }

    /// Handle unhook sequences (mostly unimplemented)
    fn unhook(&mut self) {
        // TODO: Implement unhook handling if needed
    }

    /// Handle OSC sequences (mostly unimplemented)
    fn osc_dispatch(&mut self, _params: &[&[u8]], _bell_terminated: bool) {
        // TODO: Implement OSC handling for things like setting window title
    }
}
