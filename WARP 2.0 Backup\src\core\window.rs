//! Window management for X Terminal
//! 
//! This module handles window creation, event handling, and lifecycle management
//! using winit for cross-platform window support.

use anyhow::Result;
use winit::{
    application::ApplicationHandler,
    event::{ElementState, KeyEvent, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{KeyC<PERSON>, PhysicalKey},
    window::{Window, WindowId, WindowAttributes},
};
use std::sync::Arc;
use crate::config::ConfigManager;
use crate::rendering::RenderingManager;

/// Window manager for handling the main application window
pub struct WindowManager {
    /// Configuration
    config: Arc<ConfigManager>,
    /// Main window
    window: Option<Arc<Window>>,
    /// Rendering manager
    rendering: Option<RenderingManager>,
    /// Window size
    size: (u32, u32),
    /// Whether the window should close
    should_close: bool,
}

/// Application state for winit event handling
pub struct XTerminalApp {
    /// Window manager
    window_manager: WindowManager,
}

impl WindowManager {
    /// Create a new window manager
    pub fn new(config: Arc<ConfigManager>) -> Self {
        Self {
            config,
            window: None,
            rendering: None,
            size: (1200, 800),
            should_close: false,
        }
    }
    
    /// Run the window event loop
    pub async fn run(mut self) -> Result<()> {
        tracing::info!("Starting window manager...");
        
        let event_loop = EventLoop::new()?;
        event_loop.set_control_flow(ControlFlow::Poll);
        
        let mut app = XTerminalApp {
            window_manager: self,
        };
        
        event_loop.run_app(&mut app)?;
        
        Ok(())
    }
    
    /// Create the main window
    async fn create_window(&mut self, event_loop: &ActiveEventLoop) -> Result<()> {
        let window_attributes = WindowAttributes::default()
            .with_title("X Terminal")
            .with_inner_size(winit::dpi::LogicalSize::new(self.size.0, self.size.1))
            .with_min_inner_size(winit::dpi::LogicalSize::new(800, 600));
        
        let window = Arc::new(event_loop.create_window(window_attributes)?);
        
        // Initialize rendering
        let rendering = RenderingManager::new().await?;
        
        self.window = Some(window);
        self.rendering = Some(rendering);
        
        tracing::info!("Window created successfully");
        Ok(())
    }
    
    /// Handle window resize
    fn handle_resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        self.size = (new_size.width, new_size.height);
        
        if let Some(ref mut rendering) = self.rendering {
            rendering.resize(new_size.width, new_size.height);
        }
        
        tracing::debug!("Window resized to {}x{}", new_size.width, new_size.height);
    }
    
    /// Handle keyboard input
    fn handle_keyboard_input(&mut self, event: KeyEvent) {
        match event.physical_key {
            PhysicalKey::Code(KeyCode::Escape) => {
                if event.state == ElementState::Pressed {
                    tracing::info!("Escape pressed, closing application");
                    self.should_close = true;
                }
            }
            PhysicalKey::Code(KeyCode::KeyQ) => {
                if event.state == ElementState::Pressed {
                    // Check for Ctrl+Q
                    tracing::info!("Q pressed");
                    // TODO: Check for modifiers
                }
            }
            _ => {
                tracing::trace!("Key event: {:?}", event);
            }
        }
    }
    
    /// Render a frame
    fn render(&mut self) -> Result<()> {
        if let Some(ref mut rendering) = self.rendering {
            rendering.render()?;
        }
        Ok(())
    }
    
    /// Get window reference
    pub fn window(&self) -> Option<&Arc<Window>> {
        self.window.as_ref()
    }
    
    /// Check if window should close
    pub fn should_close(&self) -> bool {
        self.should_close
    }
    
    /// Request window close
    pub fn close(&mut self) {
        self.should_close = true;
    }
}

impl ApplicationHandler for XTerminalApp {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.window_manager.window.is_none() {
            if let Err(e) = pollster::block_on(self.window_manager.create_window(event_loop)) {
                tracing::error!("Failed to create window: {}", e);
                event_loop.exit();
            }
        }
    }
    
    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        match event {
            WindowEvent::CloseRequested => {
                tracing::info!("Close requested");
                self.window_manager.should_close = true;
                event_loop.exit();
            }
            WindowEvent::Resized(physical_size) => {
                self.window_manager.handle_resize(physical_size);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                self.window_manager.handle_keyboard_input(event);
                
                if self.window_manager.should_close {
                    event_loop.exit();
                }
            }
            WindowEvent::RedrawRequested => {
                if let Err(e) = self.window_manager.render() {
                    tracing::error!("Render error: {}", e);
                }
                
                // Request next frame
                if let Some(ref window) = self.window_manager.window {
                    window.request_redraw();
                }
            }
            _ => {}
        }
    }
    
    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Request redraw
        if let Some(ref window) = self.window_manager.window {
            window.request_redraw();
        }
    }
}

/// Window configuration
#[derive(Debug, Clone)]
pub struct WindowConfig {
    pub title: String,
    pub width: u32,
    pub height: u32,
    pub min_width: u32,
    pub min_height: u32,
    pub resizable: bool,
    pub maximized: bool,
    pub fullscreen: bool,
}

impl Default for WindowConfig {
    fn default() -> Self {
        Self {
            title: "X Terminal".to_string(),
            width: 1200,
            height: 800,
            min_width: 800,
            min_height: 600,
            resizable: true,
            maximized: false,
            fullscreen: false,
        }
    }
}

/// Window events
#[derive(Debug, Clone)]
pub enum WindowEventType {
    /// Window was created
    Created,
    /// Window was resized
    Resized { width: u32, height: u32 },
    /// Window gained focus
    Focused,
    /// Window lost focus
    Unfocused,
    /// Window close was requested
    CloseRequested,
    /// Window was minimized
    Minimized,
    /// Window was maximized
    Maximized,
    /// Window entered fullscreen
    Fullscreen,
    /// Window exited fullscreen
    Windowed,
}

/// Window event handler trait
pub trait WindowEventHandler {
    /// Handle window event
    fn handle_window_event(&mut self, event: WindowEventType) -> Result<()>;
}
