//! Text buffer implementation
//! 
//! This module implements the text buffer with efficient text operations.

use anyhow::Result;
use ropey::Rope;
use super::cursor::Cursor;

/// Text buffer using rope data structure for efficient editing
pub struct TextBuffer {
    /// Rope for efficient text storage and manipulation
    rope: Rope,
}

/// Edit operation for undo/redo
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Edit {
    pub start: usize,
    pub end: usize,
    pub text: String,
    pub cursor_before: usize,
    pub cursor_after: usize,
}

impl TextBuffer {
    /// Create a new empty text buffer
    pub fn new() -> Self {
        Self {
            rope: Rope::new(),
        }
    }
    
    /// Get the text content as a string
    pub fn text(&self) -> &str {
        // For small buffers, we can use to_string, but for efficiency
        // we might want to return a rope slice in the future
        static EMPTY: &str = "";
        if self.rope.len_chars() == 0 {
            EMPTY
        } else {
            // This is a simplified implementation
            // In practice, we'd want to avoid this allocation
            Box::leak(self.rope.to_string().into_boxed_str())
        }
    }
    
    /// Set the entire text content
    pub fn set_text(&mut self, text: String) {
        self.rope = Rope::from_str(&text);
    }
    
    /// Get text length in characters
    pub fn len(&self) -> usize {
        self.rope.len_chars()
    }
    
    /// Check if buffer is empty
    pub fn is_empty(&self) -> bool {
        self.rope.len_chars() == 0
    }
    
    /// Insert text at cursor position
    pub fn insert_at_cursor(&mut self, cursor: &mut Cursor, text: &str) -> Result<Edit> {
        let pos = cursor.position();
        let edit = Edit {
            start: pos,
            end: pos,
            text: text.to_string(),
            cursor_before: pos,
            cursor_after: pos + text.chars().count(),
        };
        
        self.rope.insert(pos, text);
        cursor.set_position(edit.cursor_after);
        
        Ok(edit)
    }
    
    /// Delete character at cursor position
    pub fn delete_at_cursor(&mut self, cursor: &mut Cursor) -> Result<Option<Edit>> {
        let pos = cursor.position();
        if pos >= self.rope.len_chars() {
            return Ok(None);
        }
        
        let char_to_delete = self.rope.char(pos);
        let edit = Edit {
            start: pos,
            end: pos + 1,
            text: String::new(),
            cursor_before: pos,
            cursor_after: pos,
        };
        
        self.rope.remove(pos..pos + 1);
        
        Ok(Some(edit))
    }
    
    /// Delete character before cursor (backspace)
    pub fn backspace_at_cursor(&mut self, cursor: &mut Cursor) -> Result<Option<Edit>> {
        let pos = cursor.position();
        if pos == 0 {
            return Ok(None);
        }
        
        let new_pos = pos - 1;
        let char_to_delete = self.rope.char(new_pos);
        let edit = Edit {
            start: new_pos,
            end: pos,
            text: String::new(),
            cursor_before: pos,
            cursor_after: new_pos,
        };
        
        self.rope.remove(new_pos..pos);
        cursor.set_position(new_pos);
        
        Ok(Some(edit))
    }
    
    /// Delete text in range
    pub fn delete_range(&mut self, range: std::ops::Range<usize>) -> Result<Edit> {
        let deleted_text = self.rope.slice(range.clone()).to_string();
        let edit = Edit {
            start: range.start,
            end: range.end,
            text: String::new(),
            cursor_before: range.end,
            cursor_after: range.start,
        };
        
        self.rope.remove(range);
        
        Ok(edit)
    }
    
    /// Get text in range
    pub fn text_in_range(&self, range: std::ops::Range<usize>) -> String {
        self.rope.slice(range).to_string()
    }
    
    /// Apply an edit operation
    pub fn apply_edit(&mut self, edit: &Edit) -> Result<()> {
        if edit.text.is_empty() {
            // Delete operation
            self.rope.remove(edit.start..edit.end);
        } else if edit.start == edit.end {
            // Insert operation
            self.rope.insert(edit.start, &edit.text);
        } else {
            // Replace operation
            self.rope.remove(edit.start..edit.end);
            self.rope.insert(edit.start, &edit.text);
        }
        
        Ok(())
    }
    
    /// Apply reverse edit operation (for undo)
    pub fn apply_reverse_edit(&mut self, edit: &Edit) -> Result<()> {
        if edit.text.is_empty() {
            // Original was delete, so we insert
            // We need to figure out what was deleted - this is simplified
            // In practice, we'd store the deleted text in the edit
        } else if edit.start == edit.end {
            // Original was insert, so we delete
            let end = edit.start + edit.text.chars().count();
            self.rope.remove(edit.start..end);
        } else {
            // Original was replace, so we reverse it
            let current_end = edit.start + edit.text.chars().count();
            self.rope.remove(edit.start..current_end);
            // We'd need to restore the original text here
        }
        
        Ok(())
    }
    
    /// Get character at position
    pub fn char_at(&self, pos: usize) -> Option<char> {
        if pos < self.rope.len_chars() {
            Some(self.rope.char(pos))
        } else {
            None
        }
    }
    
    /// Get line at position
    pub fn line_at_pos(&self, pos: usize) -> usize {
        self.rope.char_to_line(pos.min(self.rope.len_chars().saturating_sub(1)))
    }
    
    /// Get column at position
    pub fn column_at_pos(&self, pos: usize) -> usize {
        let line = self.line_at_pos(pos);
        let line_start = self.rope.line_to_char(line);
        pos - line_start
    }
    
    /// Get position from line and column
    pub fn pos_from_line_col(&self, line: usize, col: usize) -> usize {
        let line_start = self.rope.line_to_char(line.min(self.rope.len_lines().saturating_sub(1)));
        let line_len = self.rope.line(line).len_chars();
        line_start + col.min(line_len.saturating_sub(1))
    }
    
    /// Get number of lines
    pub fn line_count(&self) -> usize {
        self.rope.len_lines()
    }
    
    /// Get line content
    pub fn line_content(&self, line: usize) -> String {
        if line < self.rope.len_lines() {
            self.rope.line(line).to_string()
        } else {
            String::new()
        }
    }
    
    /// Find word boundaries at position
    pub fn word_boundaries_at(&self, pos: usize) -> (usize, usize) {
        let mut start = pos;
        let mut end = pos;
        
        // Find word start
        while start > 0 {
            let ch = self.rope.char(start - 1);
            if !ch.is_alphanumeric() && ch != '_' {
                break;
            }
            start -= 1;
        }
        
        // Find word end
        while end < self.rope.len_chars() {
            let ch = self.rope.char(end);
            if !ch.is_alphanumeric() && ch != '_' {
                break;
            }
            end += 1;
        }
        
        (start, end)
    }
    
    /// Clear all content
    pub fn clear(&mut self) {
        self.rope = Rope::new();
    }
}
