//! Text editor implementation
//!
//! This module implements the full-featured text editor for command input
//! with modern editing capabilities, syntax highlighting, and AI integration.

pub mod buffer;
pub mod cursor;
pub mod selection;
pub mod history;
pub mod completion;
pub mod syntax;

use anyhow::Result;
use buffer::TextBuffer;
use cursor::Cursor;
use selection::Selection;
use history::EditHistory;
use completion::CompletionEngine;
use syntax::SyntaxHighlighter;

/// Advanced text editor for command input
pub struct TextEditor {
    /// Text buffer
    buffer: TextBuffer,
    /// Cursor position
    cursor: Cursor,
    /// Text selection
    selection: Option<Selection>,
    /// Edit history for undo/redo
    history: EditHistory,
    /// Completion engine
    completion: CompletionEngine,
    /// Syntax highlighter
    syntax: SyntaxHighlighter,
    /// Editor mode
    mode: EditorMode,
    /// Configuration
    config: EditorConfig,
}

/// Editor modes
#[derive(Debug, Clone, PartialEq)]
pub enum EditorMode {
    /// Normal command input
    Command,
    /// Multi-line editing
    MultiLine,
    /// Search mode
    Search,
    /// AI prompt mode
    AIPrompt,
}

/// Editor configuration
#[derive(Debug, Clone)]
pub struct EditorConfig {
    /// Tab size
    pub tab_size: usize,
    /// Use spaces for tabs
    pub use_spaces: bool,
    /// Enable auto-completion
    pub auto_completion: bool,
    /// Enable syntax highlighting
    pub syntax_highlighting: bool,
    /// Enable line numbers
    pub line_numbers: bool,
    /// Word wrap
    pub word_wrap: bool,
}

impl Default for EditorConfig {
    fn default() -> Self {
        Self {
            tab_size: 4,
            use_spaces: true,
            auto_completion: true,
            syntax_highlighting: true,
            line_numbers: false, // Usually off for command input
            word_wrap: true,
        }
    }
}

impl TextEditor {
    /// Create a new text editor
    pub async fn new() -> Result<Self> {
        let buffer = TextBuffer::new();
        let cursor = Cursor::new();
        let history = EditHistory::new();
        let completion = CompletionEngine::new().await?;
        let syntax = SyntaxHighlighter::new().await?;
        let config = EditorConfig::default();

        Ok(Self {
            buffer,
            cursor,
            selection: None,
            history,
            completion,
            syntax,
            mode: EditorMode::Command,
            config,
        })
    }

    /// Get current text content
    pub fn text(&self) -> &str {
        self.buffer.text()
    }

    /// Set text content
    pub fn set_text(&mut self, text: String) {
        self.buffer.set_text(text);
        self.cursor.move_to_end(&self.buffer);
        self.selection = None;
        self.history.clear();
    }

    /// Insert text at cursor position
    pub fn insert_text(&mut self, text: &str) -> Result<()> {
        let edit = self.buffer.insert_at_cursor(&mut self.cursor, text)?;
        self.history.add_edit(edit);
        self.selection = None;
        Ok(())
    }

    /// Delete character at cursor
    pub fn delete_char(&mut self) -> Result<()> {
        if let Some(edit) = self.buffer.delete_at_cursor(&mut self.cursor)? {
            self.history.add_edit(edit);
        }
        self.selection = None;
        Ok(())
    }

    /// Delete character before cursor (backspace)
    pub fn backspace(&mut self) -> Result<()> {
        if let Some(edit) = self.buffer.backspace_at_cursor(&mut self.cursor)? {
            self.history.add_edit(edit);
        }
        self.selection = None;
        Ok(())
    }

    /// Move cursor
    pub fn move_cursor(&mut self, direction: CursorMovement) {
        self.cursor.move_cursor(&self.buffer, direction);
        if !matches!(direction, CursorMovement::SelectLeft | CursorMovement::SelectRight |
                     CursorMovement::SelectUp | CursorMovement::SelectDown) {
            self.selection = None;
        }
    }

    /// Start text selection
    pub fn start_selection(&mut self) {
        self.selection = Some(Selection::new(self.cursor.position()));
    }

    /// Update selection
    pub fn update_selection(&mut self) {
        if let Some(ref mut selection) = self.selection {
            selection.update_end(self.cursor.position());
        }
    }

    /// Get selected text
    pub fn selected_text(&self) -> Option<String> {
        self.selection.as_ref().map(|sel| self.buffer.text_in_range(sel.range()))
    }

    /// Delete selected text
    pub fn delete_selection(&mut self) -> Result<()> {
        if let Some(selection) = &self.selection {
            let edit = self.buffer.delete_range(selection.range())?;
            self.history.add_edit(edit);
            self.cursor.set_position(selection.start());
            self.selection = None;
        }
        Ok(())
    }

    /// Undo last edit
    pub fn undo(&mut self) -> Result<()> {
        if let Some(edit) = self.history.undo() {
            self.buffer.apply_reverse_edit(&edit)?;
            self.cursor.set_position(edit.cursor_before);
        }
        Ok(())
    }

    /// Redo last undone edit
    pub fn redo(&mut self) -> Result<()> {
        if let Some(edit) = self.history.redo() {
            self.buffer.apply_edit(&edit)?;
            self.cursor.set_position(edit.cursor_after);
        }
        Ok(())
    }

    /// Get cursor position
    pub fn cursor_position(&self) -> usize {
        self.cursor.position()
    }

    /// Set cursor position
    pub fn set_cursor_position(&mut self, position: usize) {
        self.cursor.set_position(position.min(self.buffer.len()));
        self.selection = None;
    }

    /// Get current editor mode
    pub fn mode(&self) -> &EditorMode {
        &self.mode
    }

    /// Set editor mode
    pub fn set_mode(&mut self, mode: EditorMode) {
        self.mode = mode;
    }

    /// Get completions for current position
    pub async fn get_completions(&self) -> Result<Vec<String>> {
        self.completion.get_completions(&self.buffer, self.cursor.position()).await
    }

    /// Apply completion
    pub fn apply_completion(&mut self, completion: &str) -> Result<()> {
        // TODO: Implement smart completion application
        self.insert_text(completion)
    }

    /// Get syntax highlighting information
    pub fn get_syntax_highlighting(&self) -> Vec<SyntaxToken> {
        self.syntax.highlight(&self.buffer)
    }

    /// Check if editor has unsaved changes
    pub fn has_changes(&self) -> bool {
        self.history.has_changes()
    }

    /// Clear all content and history
    pub fn clear(&mut self) {
        self.buffer.clear();
        self.cursor = Cursor::new();
        self.selection = None;
        self.history.clear();
    }

    /// Handle key input with modifiers
    pub fn handle_key_with_modifiers(&mut self, key: &str, ctrl: bool, shift: bool, alt: bool) -> Result<()> {
        match key {
            "Enter" => {
                if ctrl {
                    // Ctrl+Enter: Execute command (placeholder)
                    tracing::info!("Execute command: {}", self.text());
                } else {
                    self.insert_text("\n")?;
                }
            }
            "Backspace" => {
                if ctrl {
                    // Ctrl+Backspace: Delete word (placeholder)
                    self.backspace()?;
                } else {
                    self.backspace()?;
                }
            }
            "Delete" => {
                self.delete_char()?;
            }
            "Tab" => {
                if shift {
                    // Shift+Tab: Unindent (placeholder)
                    self.insert_text("    ")?; // For now, just insert spaces
                } else {
                    self.insert_text("    ")?; // Insert 4 spaces for tab
                }
            }
            // Ctrl key combinations
            "a" if ctrl => {
                // Select all (placeholder)
                tracing::debug!("Select all");
            }
            "c" if ctrl => {
                // Copy (placeholder)
                tracing::debug!("Copy");
            }
            "v" if ctrl => {
                // Paste (placeholder)
                tracing::debug!("Paste");
            }
            "x" if ctrl => {
                // Cut (placeholder)
                tracing::debug!("Cut");
            }
            "z" if ctrl => {
                if shift {
                    self.redo()?;
                } else {
                    self.undo()?;
                }
            }
            "y" if ctrl => {
                self.redo()?;
            }
            _ => {
                // Insert regular characters
                if !ctrl && !alt && key.len() == 1 {
                    self.insert_text(key)?;
                }
            }
        }

        Ok(())
    }

    /// Handle key input (simplified version)
    pub fn handle_key(&mut self, key: &str) -> Result<()> {
        self.handle_key_with_modifiers(key, false, false, false)
    }
}

/// Cursor movement directions
#[derive(Debug, Clone, Copy)]
pub enum CursorMovement {
    Left,
    Right,
    Up,
    Down,
    WordLeft,
    WordRight,
    LineStart,
    LineEnd,
    DocumentStart,
    DocumentEnd,
    SelectLeft,
    SelectRight,
    SelectUp,
    SelectDown,
    SelectWordLeft,
    SelectWordRight,
    SelectLineStart,
    SelectLineEnd,
    SelectAll,
}

/// Syntax highlighting token
#[derive(Debug, Clone)]
pub struct SyntaxToken {
    pub start: usize,
    pub end: usize,
    pub token_type: TokenType,
}

/// Token types for syntax highlighting
#[derive(Debug, Clone)]
pub enum TokenType {
    Command,
    Argument,
    Flag,
    Path,
    String,
    Number,
    Variable,
    Comment,
    Operator,
    Keyword,
}
