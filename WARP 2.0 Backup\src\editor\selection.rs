//! Text selection implementation
//! 
//! This module handles text selection for copy/paste operations.

use std::ops::Range;
use super::buffer::TextBuffer;

/// Text selection
#[derive(Debug, <PERSON><PERSON>)]
pub struct Selection {
    /// Selection start position
    start: usize,
    /// Selection end position
    end: usize,
}

impl Selection {
    /// Create a new selection starting at position
    pub fn new(start: usize) -> Self {
        Self { start, end: start }
    }
    
    /// Update the end position of the selection
    pub fn update_end(&mut self, end: usize) {
        self.end = end;
    }
    
    /// Get the start position (always the smaller value)
    pub fn start(&self) -> usize {
        self.start.min(self.end)
    }
    
    /// Get the end position (always the larger value)
    pub fn end(&self) -> usize {
        self.start.max(self.end)
    }
    
    /// Get the selection as a range
    pub fn range(&self) -> Range<usize> {
        self.start()..self.end()
    }
    
    /// Get the length of the selection
    pub fn len(&self) -> usize {
        self.end() - self.start()
    }
    
    /// Check if the selection is empty
    pub fn is_empty(&self) -> bool {
        self.start == self.end
    }
    
    /// Check if the selection contains a position
    pub fn contains(&self, pos: usize) -> bool {
        pos >= self.start() && pos < self.end()
    }
    
    /// Extend the selection to include a position
    pub fn extend_to(&mut self, pos: usize) {
        if pos < self.start {
            self.start = pos;
        } else if pos > self.end {
            self.end = pos;
        }
    }
    
    /// Set the selection to cover a range
    pub fn set_range(&mut self, start: usize, end: usize) {
        self.start = start;
        self.end = end;
    }
    
    /// Clear the selection (make it empty at current position)
    pub fn clear(&mut self) {
        self.end = self.start;
    }
    
    /// Get the direction of the selection
    pub fn direction(&self) -> SelectionDirection {
        if self.start <= self.end {
            SelectionDirection::Forward
        } else {
            SelectionDirection::Backward
        }
    }
    
    /// Get the anchor position (where selection started)
    pub fn anchor(&self) -> usize {
        match self.direction() {
            SelectionDirection::Forward => self.start,
            SelectionDirection::Backward => self.end,
        }
    }
    
    /// Get the active position (where selection is being extended to)
    pub fn active(&self) -> usize {
        match self.direction() {
            SelectionDirection::Forward => self.end,
            SelectionDirection::Backward => self.start,
        }
    }

    /// Get selected text
    pub fn get_text(&self, buffer: &TextBuffer) -> String {
        buffer.text_in_range(self.start..self.end)
    }

    /// Check if selection is active
    pub fn is_active(&self) -> bool {
        self.start != self.end
    }

    /// Start selection at current position
    pub fn start_selection(&mut self, position: usize) {
        self.start = position;
        self.end = position;
    }



    /// Select all text
    pub fn select_all(&mut self, buffer: &TextBuffer) {
        self.start = 0;
        self.end = buffer.len();
    }

    /// Select current line
    pub fn select_line(&mut self, buffer: &TextBuffer, cursor_position: usize) {
        let line_num = buffer.line_at_pos(cursor_position);
        let line_start = buffer.pos_from_line_col(line_num, 0);
        let line_content = buffer.line_content(line_num);
        let line_end = line_start + line_content.len();

        self.start = line_start;
        self.end = line_end;
    }

    /// Select current word
    pub fn select_word(&mut self, buffer: &TextBuffer, cursor_position: usize) {
        let text = buffer.text();
        let mut start = cursor_position;
        let mut end = cursor_position;

        // Find word start
        while start > 0 {
            let ch = text.chars().nth(start - 1).unwrap_or(' ');
            if ch.is_whitespace() || ch.is_ascii_punctuation() {
                break;
            }
            start -= 1;
        }

        // Find word end
        while end < text.len() {
            let ch = text.chars().nth(end).unwrap_or(' ');
            if ch.is_whitespace() || ch.is_ascii_punctuation() {
                break;
            }
            end += 1;
        }

        self.start = start;
        self.end = end;
    }
}

/// Selection direction
#[derive(Debug, Clone, PartialEq)]
pub enum SelectionDirection {
    Forward,
    Backward,
}
