//! X Terminal - A high-performance terminal with enhanced AI capabilities
//! 
//! This is a comprehensive Warp Terminal clone built in Rust with enhanced AI features,
//! multiple LLM provider support, and advanced development capabilities.

pub mod core;
pub mod rendering;
pub mod ui;
pub mod editor;
pub mod ai;
pub mod blocks;
pub mod collaboration;
pub mod config;
pub mod themes;
pub mod plugins;
pub mod performance;

use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Main application state
pub struct XTerminal {
    /// Configuration manager
    pub config: Arc<config::ConfigManager>,
    /// Terminal core engine
    pub terminal: Arc<RwLock<core::Terminal>>,
    /// Block management system
    pub blocks: Arc<RwLock<blocks::BlockManager>>,
    /// AI integration layer
    pub ai: Arc<ai::AIManager>,
    /// Collaboration features
    pub collaboration: Arc<collaboration::CollaborationManager>,
    /// Theme manager
    pub themes: Arc<themes::ThemeManager>,
}

impl XTerminal {
    /// Create a new X Terminal instance
    pub async fn new() -> Result<Self> {
        let config = Arc::new(config::ConfigManager::new().await?);
        let terminal = Arc::new(RwLock::new(core::Terminal::new(&config).await?));
        let blocks = Arc::new(RwLock::new(blocks::BlockManager::new()));
        let ai = Arc::new(ai::AIManager::new(&config).await?);
        let collaboration = Arc::new(collaboration::CollaborationManager::new(&config).await?);
        let themes = Arc::new(themes::ThemeManager::new(&config).await?);

        Ok(Self {
            config,
            terminal,
            blocks,
            ai,
            collaboration,
            themes,
        })
    }

    /// Start the terminal application
    pub async fn run(&self) -> Result<()> {
        tracing::info!("Starting X Terminal...");
        
        // Initialize all subsystems
        self.initialize().await?;
        
        // Start the main event loop
        self.event_loop().await?;
        
        Ok(())
    }

    /// Initialize all subsystems
    async fn initialize(&self) -> Result<()> {
        tracing::info!("Initializing X Terminal subsystems...");
        
        // Initialize terminal core
        {
            let mut terminal = self.terminal.write().await;
            terminal.initialize().await?;
        }
        
        // Initialize AI system
        self.ai.initialize().await?;
        
        // Initialize collaboration features
        self.collaboration.initialize().await?;
        
        tracing::info!("All subsystems initialized successfully");
        Ok(())
    }

    /// Main event loop
    async fn event_loop(&self) -> Result<()> {
        tracing::info!("Starting main event loop...");
        
        // This will be implemented with the rendering system
        // For now, just keep the application running
        loop {
            tokio::time::sleep(tokio::time::Duration::from_millis(16)).await; // ~60 FPS
            
            // Handle terminal events
            {
                let mut terminal = self.terminal.write().await;
                terminal.process_events().await?;
            }
            
            // Process AI requests
            self.ai.process_pending_requests().await?;
            
            // Handle collaboration events
            self.collaboration.process_events().await?;
        }
    }
}

/// Application error types
#[derive(thiserror::Error, Debug)]
pub enum XTerminalError {
    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),
    
    #[error("Terminal error: {0}")]
    Terminal(#[from] core::TerminalError),
    
    #[error("AI error: {0}")]
    AI(#[from] ai::AIError),
    
    #[error("Rendering error: {0}")]
    Rendering(#[from] rendering::RenderingError),
    
    #[error("Collaboration error: {0}")]
    Collaboration(#[from] collaboration::CollaborationError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Other error: {0}")]
    Other(#[from] anyhow::Error),
}

pub type XResult<T> = Result<T, XTerminalError>;

/// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");
