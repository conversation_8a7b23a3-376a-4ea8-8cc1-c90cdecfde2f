//! GPU context and initialization
//! 
//! This module handles GPU initialization, device creation, and surface management.

use anyhow::Result;
use wgpu::{Adapter, Device, Instance, Queue, TextureFormat};
use winit::window::Window;
use std::sync::Arc;

/// GPU context containing device and queue
pub struct GpuContext {
    /// WGPU instance
    pub instance: Instance,
    /// GPU adapter
    pub adapter: Adapter,
    /// GPU device
    pub device: Device,
    /// Command queue
    pub queue: Queue,
    /// Surface format
    pub surface_format: TextureFormat,
}

impl GpuContext {
    /// Create a new GPU context
    pub async fn new() -> Result<Self> {
        tracing::info!("Initializing GPU context...");
        
        // Create WGPU instance
        let instance = Instance::new(wgpu::InstanceDescriptor {
            backends: wgpu::Backends::all(),
            flags: wgpu::InstanceFlags::default(),
            dx12_shader_compiler: wgpu::Dx12Compiler::default(),
            gles_minor_version: wgpu::Gles3MinorVersion::Automatic,
        });
        
        // Request adapter
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::HighPerformance,
                compatible_surface: None, // We'll handle surface separately
                force_fallback_adapter: false,
            })
            .await
            .ok_or_else(|| anyhow::anyhow!("Failed to find suitable GPU adapter"))?;

        tracing::info!("Selected GPU adapter: {:?}", adapter.get_info());

        // Use a default surface format
        let surface_format = wgpu::TextureFormat::Bgra8UnormSrgb;
        
        tracing::info!("Surface format: {:?}", surface_format);
        
        // Request device and queue
        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    label: Some("X Terminal Device"),
                    required_features: wgpu::Features::empty(),
                    required_limits: wgpu::Limits::default(),
                },
                None,
            )
            .await?;
        
        tracing::info!("GPU context initialized successfully");
        
        Ok(Self {
            instance,
            adapter,
            device,
            queue,
            surface_format,
        })
    }
    
    /// Get device limits
    pub fn limits(&self) -> wgpu::Limits {
        self.device.limits()
    }
    
    /// Get adapter info
    pub fn adapter_info(&self) -> wgpu::AdapterInfo {
        self.adapter.get_info()
    }
    
    /// Create a shader module from WGSL source
    pub fn create_shader_module(&self, source: &str, label: Option<&str>) -> wgpu::ShaderModule {
        self.device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label,
            source: wgpu::ShaderSource::Wgsl(source.into()),
        })
    }
    
    /// Create a buffer
    pub fn create_buffer(&self, descriptor: &wgpu::BufferDescriptor) -> wgpu::Buffer {
        self.device.create_buffer(descriptor)
    }
    
    /// Create a texture
    pub fn create_texture(&self, descriptor: &wgpu::TextureDescriptor) -> wgpu::Texture {
        self.device.create_texture(descriptor)
    }
    
    /// Create a bind group layout
    pub fn create_bind_group_layout(&self, descriptor: &wgpu::BindGroupLayoutDescriptor) -> wgpu::BindGroupLayout {
        self.device.create_bind_group_layout(descriptor)
    }
    
    /// Create a render pipeline
    pub fn create_render_pipeline(&self, descriptor: &wgpu::RenderPipelineDescriptor) -> wgpu::RenderPipeline {
        self.device.create_render_pipeline(descriptor)
    }
}
