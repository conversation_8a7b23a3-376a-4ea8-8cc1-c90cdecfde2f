//! Layout management for X Terminal UI
//! 
//! This module handles the layout and positioning of UI components,
//! including responsive design and window resizing.

use anyhow::Result;
use super::components::{ComponentBounds};
use std::collections::HashMap;
use uuid::Uuid;

/// Layout manager for handling UI layout
pub struct LayoutManager {
    /// Current window size
    window_size: (u32, u32),
    /// Layout mode
    layout_mode: LayoutMode,
    /// Margins and padding
    margins: Margins,
    /// Component registry
    components: HashMap<Uuid, ComponentBounds>,
    /// Layout constraints
    constraints: LayoutConstraints,
    /// Dirty flag for re-layout
    needs_layout: bool,
}

/// Layout modes
#[derive(Debug, Clone)]
pub enum LayoutMode {
    /// Standard terminal layout
    Standard,
    /// Split view with sidebar
    SplitView,
    /// Full screen mode
    FullScreen,
    /// Compact mode for small screens
    Compact,
}

/// Margins and padding configuration
#[derive(Debug, <PERSON>lone)]
pub struct Margins {
    pub top: f32,
    pub bottom: f32,
    pub left: f32,
    pub right: f32,
    pub block_spacing: f32,
    pub component_padding: f32,
}

impl Default for Margins {
    fn default() -> Self {
        Self {
            top: 10.0,
            bottom: 10.0,
            left: 10.0,
            right: 10.0,
            block_spacing: 10.0,
            component_padding: 5.0,
        }
    }
}

/// Layout constraints for components
#[derive(Debug, Clone)]
pub struct LayoutConstraints {
    /// Minimum width
    pub min_width: f32,
    /// Maximum width
    pub max_width: f32,
    /// Minimum height
    pub min_height: f32,
    /// Maximum height
    pub max_height: f32,
    /// Aspect ratio (width/height)
    pub aspect_ratio: Option<f32>,
}

impl Default for LayoutConstraints {
    fn default() -> Self {
        Self {
            min_width: 0.0,
            max_width: f32::INFINITY,
            min_height: 0.0,
            max_height: f32::INFINITY,
            aspect_ratio: None,
        }
    }
}

impl LayoutManager {
    /// Create a new layout manager
    pub async fn new() -> Result<Self> {
        Ok(Self {
            window_size: (800, 600),
            layout_mode: LayoutMode::Standard,
            margins: Margins::default(),
            components: HashMap::new(),
            constraints: LayoutConstraints::default(),
            needs_layout: true,
        })
    }
    
    /// Update layout
    pub async fn update(&mut self) -> Result<()> {
        // TODO: Implement layout updates
        Ok(())
    }
    
    /// Set window size
    pub fn set_window_size(&mut self, width: u32, height: u32) {
        self.window_size = (width, height);
        self.update_layout_mode();
    }
    
    /// Update layout mode based on window size
    fn update_layout_mode(&mut self) {
        let (width, height) = self.window_size;
        
        self.layout_mode = if width < 600 || height < 400 {
            LayoutMode::Compact
        } else if width > 1200 {
            LayoutMode::SplitView
        } else {
            LayoutMode::Standard
        };
    }
    
    /// Calculate component bounds for input area
    pub fn calculate_input_bounds(&self) -> ComponentBounds {
        let (width, height) = self.window_size;
        
        match self.layout_mode {
            LayoutMode::Compact => ComponentBounds {
                x: self.margins.left,
                y: (height as f32) - 40.0 - self.margins.bottom,
                width: (width as f32) - self.margins.left - self.margins.right,
                height: 30.0,
            },
            _ => ComponentBounds {
                x: self.margins.left,
                y: self.margins.top,
                width: (width as f32) - self.margins.left - self.margins.right,
                height: 35.0,
            },
        }
    }
    
    /// Calculate component bounds for status bar
    pub fn calculate_status_bar_bounds(&self) -> ComponentBounds {
        let (width, height) = self.window_size;
        
        ComponentBounds {
            x: 0.0,
            y: (height as f32) - 25.0,
            width: width as f32,
            height: 25.0,
        }
    }
    
    /// Calculate component bounds for sidebar
    pub fn calculate_sidebar_bounds(&self) -> ComponentBounds {
        let (width, height) = self.window_size;
        let sidebar_width = match self.layout_mode {
            LayoutMode::SplitView => 300.0,
            LayoutMode::Compact => 200.0,
            _ => 250.0,
        };
        
        ComponentBounds {
            x: (width as f32) - sidebar_width,
            y: self.margins.top,
            width: sidebar_width,
            height: (height as f32) - self.margins.top - 25.0, // Account for status bar
        }
    }
    
    /// Calculate available area for blocks
    pub fn calculate_blocks_area(&self, sidebar_visible: bool) -> ComponentBounds {
        let (width, height) = self.window_size;
        let input_bounds = self.calculate_input_bounds();
        
        let available_width = if sidebar_visible {
            let sidebar_bounds = self.calculate_sidebar_bounds();
            sidebar_bounds.x - self.margins.left
        } else {
            (width as f32) - self.margins.left - self.margins.right
        };
        
        ComponentBounds {
            x: self.margins.left,
            y: input_bounds.y + input_bounds.height + self.margins.block_spacing,
            width: available_width,
            height: (height as f32) - (input_bounds.y + input_bounds.height) - 25.0 - self.margins.bottom - self.margins.block_spacing,
        }
    }
    
    /// Calculate block bounds within the blocks area
    pub fn calculate_block_bounds(&self, index: usize, block_height: f32, blocks_area: &ComponentBounds) -> ComponentBounds {
        let y_offset = (index as f32) * (block_height + self.margins.block_spacing);
        
        ComponentBounds {
            x: blocks_area.x,
            y: blocks_area.y + y_offset,
            width: blocks_area.width,
            height: block_height,
        }
    }
    
    /// Get current layout mode
    pub fn layout_mode(&self) -> &LayoutMode {
        &self.layout_mode
    }
    
    /// Set layout mode
    pub fn set_layout_mode(&mut self, mode: LayoutMode) {
        self.layout_mode = mode;
    }
    
    /// Get margins
    pub fn margins(&self) -> &Margins {
        &self.margins
    }
    
    /// Set margins
    pub fn set_margins(&mut self, margins: Margins) {
        self.margins = margins;
    }
    
    /// Calculate optimal block height based on content
    pub fn calculate_block_height(&self, content_lines: usize) -> f32 {
        let header_height = 40.0;
        let line_height = 16.0;
        let min_content_height = 20.0;
        
        let content_height = (content_lines as f32 * line_height).max(min_content_height);
        header_height + content_height + (self.margins.component_padding * 2.0)
    }
    
    /// Check if point is in scrollable area
    pub fn is_scrollable_area(&self, x: f32, y: f32, sidebar_visible: bool) -> bool {
        let blocks_area = self.calculate_blocks_area(sidebar_visible);
        x >= blocks_area.x && x <= blocks_area.x + blocks_area.width &&
        y >= blocks_area.y && y <= blocks_area.y + blocks_area.height
    }

    /// Register a component with the layout manager
    pub fn register_component(&mut self, id: Uuid, bounds: ComponentBounds) {
        self.components.insert(id, bounds);
        self.needs_layout = true;
    }

    /// Unregister a component
    pub fn unregister_component(&mut self, id: &Uuid) {
        self.components.remove(id);
        self.needs_layout = true;
    }

    /// Update component bounds
    pub fn update_component_bounds(&mut self, id: &Uuid, bounds: ComponentBounds) {
        if let Some(existing_bounds) = self.components.get_mut(id) {
            *existing_bounds = bounds;
            self.needs_layout = true;
        }
    }

    /// Get component bounds
    pub fn get_component_bounds(&self, id: &Uuid) -> Option<&ComponentBounds> {
        self.components.get(id)
    }

    /// Set layout constraints
    pub fn set_constraints(&mut self, constraints: LayoutConstraints) {
        self.constraints = constraints;
        self.needs_layout = true;
    }

    /// Perform layout calculations
    pub fn perform_layout(&mut self) {
        if !self.needs_layout {
            return;
        }

        // Layout calculations would go here
        // For now, this is a placeholder

        self.needs_layout = false;
    }
}
