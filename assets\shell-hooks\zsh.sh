#!/bin/zsh
# X Terminal Zsh Integration Hooks
# This script sets up shell integration for zsh to enable block-based command tracking

# Check if we're already integrated
if [[ -n "$X_TERMINAL_INTEGRATED" ]]; then
    return 0
fi

export X_TERMINAL_INTEGRATED=1
export X_TERMINAL_SESSION_ID="${X_TERMINAL_SESSION_ID:-$(uuidgen 2>/dev/null || echo "zsh-$$-$(date +%s)")}"

# Function to send hook events to X Terminal
__x_terminal_send_hook() {
    local hook_type="$1"
    local data="$2"
    
    # Send OSC sequence to terminal
    printf '\033]1337;XTerminalHook=%s;SessionId=%s;Data=%s\007' \
        "$hook_type" "$X_TERMINAL_SESSION_ID" "$data"
}

# Pre-command hook (called before each prompt)
__x_terminal_precmd() {
    local exit_code=$?
    local end_time=$(date +%s%3N)
    
    # Send command completion event
    if [[ -n "$__x_terminal_cmd_start_time" ]]; then
        local duration=$((end_time - __x_terminal_cmd_start_time))
        __x_terminal_send_hook "postcmd" "ExitCode=$exit_code;Duration=$duration;EndTime=$end_time;WorkingDir=$PWD"
        unset __x_terminal_cmd_start_time
    fi
    
    # Send precmd event
    __x_terminal_send_hook "precmd" "ExitCode=$exit_code;WorkingDir=$PWD;Time=$end_time"
}

# Pre-execution hook (called before command execution)
__x_terminal_preexec() {
    local cmd="$1"
    local start_time=$(date +%s%3N)
    
    export __x_terminal_cmd_start_time="$start_time"
    
    # Send preexec event
    __x_terminal_send_hook "preexec" "Command=$cmd;StartTime=$start_time;WorkingDir=$PWD"
}

# Set up hooks using zsh's hook system
if [[ -n "$ZSH_VERSION" ]]; then
    # Add to precmd_functions array
    if [[ -z "$precmd_functions" ]]; then
        precmd_functions=()
    fi
    precmd_functions+=(__x_terminal_precmd)
    
    # Add to preexec_functions array
    if [[ -z "$preexec_functions" ]]; then
        preexec_functions=()
    fi
    preexec_functions+=(__x_terminal_preexec)
    
    # Send initialization event
    __x_terminal_send_hook "init" "Shell=zsh;Version=$ZSH_VERSION;PID=$$"
fi

# Function to disable X Terminal integration
x_terminal_disable() {
    # Remove from hook arrays
    precmd_functions=(${precmd_functions:#__x_terminal_precmd})
    preexec_functions=(${preexec_functions:#__x_terminal_preexec})
    
    unset X_TERMINAL_INTEGRATED
    unset X_TERMINAL_SESSION_ID
    unset __x_terminal_cmd_start_time
    
    # Remove functions
    unset -f __x_terminal_send_hook
    unset -f __x_terminal_precmd
    unset -f __x_terminal_preexec
    unset -f x_terminal_disable
}

# Function to get X Terminal status
x_terminal_status() {
    echo "X Terminal Integration: ${X_TERMINAL_INTEGRATED:-disabled}"
    echo "Session ID: ${X_TERMINAL_SESSION_ID:-none}"
    echo "Shell: zsh $ZSH_VERSION"
    echo "PID: $$"
    echo "Precmd functions: ${#precmd_functions[@]}"
    echo "Preexec functions: ${#preexec_functions[@]}"
}

echo "X Terminal zsh integration loaded (Session: $X_TERMINAL_SESSION_ID)"
