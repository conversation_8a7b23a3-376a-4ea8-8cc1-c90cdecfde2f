# X Terminal API Documentation

This document provides comprehensive API documentation for X Terminal's tools system and core functionality.

## 🛠 Tools API

The Tools API provides programmatic access to X Terminal's advanced functionality through a unified interface.

### Getting Started

```rust
use x_terminal::tools::{ToolsManager, ToolParams, ToolParamValue};

// Initialize the tools manager
let mut tools = ToolsManager::new().await?;
tools.initialize().await?;

// Execute a tool
let mut params = ToolParams::new();
params.params.insert("path".to_string(), ToolParamValue::String("test.txt".to_string()));
params.params.insert("content".to_string(), ToolParamValue::String("Hello, World!".to_string()));

let result = tools.execute_tool("create_file", params).await?;
println!("Result: {}", result.message);
```

### Tool Categories

#### File Operations

##### create_file
Creates a new file with the given content.

**Parameters:**
- `path` (String): File path to create
- `content` (String): Content to write to the file

**Example:**
```rust
let result = tools.execute_tool("create_file", ToolParams {
    params: hashmap! {
        "path" => ToolParamValue::String("example.txt".to_string()),
        "content" => ToolParamValue::String("Hello, World!".to_string()),
    }
}).await?;
```

##### read_file
Reads file content (all or specific lines).

**Parameters:**
- `path` (String): File path to read
- `lines` (Optional): Line range to read

**Example:**
```rust
let result = tools.execute_tool("read_file", ToolParams {
    params: hashmap! {
        "path" => ToolParamValue::String("example.txt".to_string()),
    }
}).await?;
```

##### multi_write
Writes multiple files at once.

**Parameters:**
- `paths_contents` (PathContentArray): Array of path-content pairs

**Example:**
```rust
let result = tools.execute_tool("multi_write", ToolParams {
    params: hashmap! {
        "paths_contents" => ToolParamValue::PathContentArray(vec![
            PathContent { path: "file1.txt".to_string(), content: "Content 1".to_string() },
            PathContent { path: "file2.txt".to_string(), content: "Content 2".to_string() },
        ]),
    }
}).await?;
```

#### Search Operations

##### search_files_by_pattern
Finds files using glob patterns.

**Parameters:**
- `pattern` (String): Glob pattern (e.g., "*.rs", "src/**/*.py")

**Example:**
```rust
let result = tools.execute_tool("search_files_by_pattern", ToolParams {
    params: hashmap! {
        "pattern" => ToolParamValue::String("src/**/*.rs".to_string()),
    }
}).await?;
```

##### semantic_file_search
Natural language file search.

**Parameters:**
- `query` (String): Natural language search query

**Example:**
```rust
let result = tools.execute_tool("semantic_file_search", ToolParams {
    params: hashmap! {
        "query" => ToolParamValue::String("find files where database connections are configured".to_string()),
    }
}).await?;
```

#### Terminal Operations

##### run_in_terminal
Executes any shell command.

**Parameters:**
- `command` (String): Command to execute

**Example:**
```rust
let result = tools.execute_tool("run_in_terminal", ToolParams {
    params: hashmap! {
        "command" => ToolParamValue::String("ls -la".to_string()),
    }
}).await?;
```

#### Project Operations

##### backup_project_snapshot
Creates a full project backup with a name.

**Parameters:**
- `name` (String): Name for the backup snapshot

**Example:**
```rust
let result = tools.execute_tool("backup_project_snapshot", ToolParams {
    params: hashmap! {
        "name" => ToolParamValue::String("before-refactor".to_string()),
    }
}).await?;
```

##### contextual_edit_file
AI-powered file editing based on intent.

**Parameters:**
- `intent` (String): What you want to achieve
- `file` (String): File to edit

**Example:**
```rust
let result = tools.execute_tool("contextual_edit_file", ToolParams {
    params: hashmap! {
        "intent" => ToolParamValue::String("add error handling to this function".to_string()),
        "file" => ToolParamValue::String("src/main.rs".to_string()),
    }
}).await?;
```

#### Package Operations

##### install_python_packages
Install Python packages via pip.

**Parameters:**
- `packages` (StringArray): List of Python packages to install

**Example:**
```rust
let result = tools.execute_tool("install_python_packages", ToolParams {
    params: hashmap! {
        "packages" => ToolParamValue::StringArray(vec![
            "requests".to_string(),
            "numpy".to_string(),
            "pandas".to_string(),
        ]),
    }
}).await?;
```

### Response Format

All tools return a `ToolResult` with the following structure:

```rust
pub struct ToolResult {
    pub success: bool,
    pub message: String,
    pub data: Option<ToolResultData>,
}

pub enum ToolResultData {
    String(String),
    StringArray(Vec<String>),
    FileContent(String),
    DirectoryListing(Vec<String>),
    SearchResults(Vec<SearchResult>),
    CommandOutput(CommandOutput),
}
```

### Error Handling

```rust
match tools.execute_tool("create_file", params).await {
    Ok(result) => {
        if result.success {
            println!("Success: {}", result.message);
            if let Some(data) = result.data {
                // Process result data
            }
        } else {
            eprintln!("Tool failed: {}", result.message);
        }
    }
    Err(e) => {
        eprintln!("Error executing tool: {}", e);
    }
}
```

## 🤖 AI API

### Agent Management

```rust
use x_terminal::ai::{AgentManager, AgentType};

let mut agent_manager = AgentManager::new(config).await?;

// Create an AI agent
let agent_id = agent_manager.create_agent(
    AgentType::CodeGeneration,
    "Code Generator".to_string()
).await?;

// Generate code
let result = agent_manager.generate_code(
    "Create a function to parse JSON",
    "rust"
).await?;
```

### Available Agent Types

- `ErrorAnalysis`: Analyzes errors and suggests solutions
- `CodeGeneration`: Generates code from natural language
- `CommandSuggestion`: Suggests terminal commands
- `Refactoring`: Refactors existing code
- `Documentation`: Generates documentation
- `AutonomousCoding`: Autonomous coding capabilities

### Provider Configuration

```rust
use x_terminal::ai::providers::{ProviderManager, ProviderConfig};

let provider_config = ProviderConfig {
    provider_type: "openai".to_string(),
    api_key: "your-api-key".to_string(),
    model: "gpt-4".to_string(),
    base_url: Some("https://api.openai.com/v1".to_string()),
};

let provider_manager = ProviderManager::new(config).await?;
provider_manager.add_provider("openai", provider_config).await?;
```

## 🎨 Theme API

### Theme Management

```rust
use x_terminal::themes::{ThemeManager, Theme, ThemeModifications};

let theme_manager = ThemeManager::new(config).await?;

// Apply a built-in theme
theme_manager.apply_theme("dark").await?;

// Create a custom theme
let modifications = ThemeModifications {
    background: Some("#1e1e1e".to_string()),
    foreground: Some("#d4d4d4".to_string()),
    terminal_font: Some("JetBrains Mono".to_string()),
    font_size: Some(14),
};

let custom_theme = theme_manager.create_custom_theme(
    "dark",
    "My Dark Theme".to_string(),
    modifications
)?;

// Save the custom theme
theme_manager.save_theme(&custom_theme, "my-dark-theme").await?;
```

## 🔧 Configuration API

### Configuration Management

```rust
use x_terminal::config::{ConfigManager, TerminalConfig};

let config_manager = ConfigManager::new().await?;

// Get current configuration
let config = config_manager.get_config().await?;

// Update configuration
let mut new_config = config.clone();
new_config.terminal.font_size = 16;
new_config.terminal.theme = "light".to_string();

config_manager.update_config(new_config).await?;
```

### Configuration Structure

```rust
pub struct Config {
    pub terminal: TerminalConfig,
    pub ai: AIConfig,
    pub collaboration: CollaborationConfig,
    pub performance: PerformanceConfig,
}

pub struct TerminalConfig {
    pub shell: String,
    pub font_family: String,
    pub font_size: u32,
    pub theme: String,
    pub cursor_style: CursorStyle,
    pub scrollback_lines: usize,
}
```

## 📊 Performance API

### Performance Monitoring

```rust
use x_terminal::performance::{PerformanceMonitor, PerformanceMetrics};

let mut monitor = PerformanceMonitor::new();

// Record performance metrics
monitor.record_frame_time(Duration::from_millis(16)).await;
monitor.record_render_time(Duration::from_millis(8)).await;

// Get current metrics
let metrics = monitor.get_metrics().await;
println!("FPS: {:.1}", metrics.fps);
println!("Frame time: {:.2}ms", metrics.avg_frame_time.as_secs_f64() * 1000.0);

// Check if performance is acceptable
if !monitor.is_performance_acceptable().await {
    let suggestions = monitor.optimize_performance().await;
    for suggestion in suggestions {
        println!("Optimization: {}", suggestion.description);
    }
}
```

## 🤝 Collaboration API

### Session Management

```rust
use x_terminal::collaboration::{CollaborationManager, Participant, ParticipantRole};

let collaboration_manager = CollaborationManager::new(config).await?;

// Share a session
let session_id = collaboration_manager.share_session(
    &session_uuid,
    vec!["user1".to_string(), "user2".to_string()]
).await?;

// Join a session
let participant = Participant {
    id: Uuid::new_v4(),
    username: "user3".to_string(),
    role: ParticipantRole::Member,
    joined_at: chrono::Utc::now(),
};

collaboration_manager.join_session_enhanced(&session_uuid, participant).await?;
```

## 🔌 Plugin API

### Plugin Development

```rust
use x_terminal::plugins::{Plugin, PluginManager, PluginMetadata};

pub struct MyPlugin {
    metadata: PluginMetadata,
}

impl Plugin for MyPlugin {
    fn metadata(&self) -> &PluginMetadata {
        &self.metadata
    }
    
    async fn initialize(&mut self) -> Result<()> {
        // Plugin initialization logic
        Ok(())
    }
    
    async fn execute(&self, command: &str, args: &[String]) -> Result<String> {
        // Plugin command execution
        Ok(format!("Executed: {} with args: {:?}", command, args))
    }
}

// Register plugin
let plugin_manager = PluginManager::new().await?;
plugin_manager.register_plugin(Box::new(MyPlugin::new())).await?;
```

This API documentation provides the foundation for integrating with and extending X Terminal. For more detailed examples and advanced usage, see the examples directory in the repository.
