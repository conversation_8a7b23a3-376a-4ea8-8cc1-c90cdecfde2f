//! Basic usage example for X Terminal
//! 
//! This example demonstrates how to use the main features of X Terminal.

use anyhow::Result;
use x_terminal::*;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    println!("🚀 X Terminal - Advanced Terminal with AI Integration");
    println!("Version: {}", VERSION);
    println!("Description: {}", DESCRIPTION);
    println!();
    
    // Create and initialize the terminal
    println!("📦 Initializing X Terminal...");
    let terminal = XTerminal::new().await?;
    
    if !terminal.is_initialized() {
        println!("❌ Failed to initialize terminal");
        return Ok(());
    }
    
    println!("✅ Terminal initialized successfully!");
    println!();
    
    // Demonstrate configuration management
    println!("⚙️  Configuration Management:");
    let config = config::ConfigManager::new().await?;
    
    // Get terminal configuration
    let terminal_config = config.get_terminal_config()?;
    println!("  - Shell: {:?}", terminal_config.shell);
    println!("  - Font: {}", terminal_config.font_family);
    println!("  - Font Size: {}", terminal_config.font_size);
    
    // Get AI configuration
    let ai_config = config.get_ai_config()?;
    println!("  - AI Enabled: {}", ai_config.enabled);
    println!("  - Default Provider: {:?}", ai_config.default_provider);
    
    // Get theme configuration
    let theme_config = config.get_theme_config()?;
    println!("  - Current Theme: {}", theme_config.current_theme);
    println!();
    
    // Demonstrate AI features
    println!("🤖 AI Features:");
    let ai_manager = ai::AIManager::new(&std::sync::Arc::new(config.clone())).await?;
    
    // Test command suggestions
    println!("  - Getting command suggestions for 'git'...");
    let suggestions = ai_manager.suggest_commands("git").await?;
    for (i, suggestion) in suggestions.iter().take(3).enumerate() {
        println!("    {}. {}", i + 1, suggestion);
    }
    println!();
    
    // Demonstrate theme management
    println!("🎨 Theme Management:");
    let theme_manager = themes::ThemeManager::new(&std::sync::Arc::new(config.clone())).await?;
    let current_theme = theme_manager.get_current_theme();
    println!("  - Current Theme: {}", current_theme.name);
    println!("  - Background: {:?}", current_theme.colors.background);
    println!("  - Foreground: {:?}", current_theme.colors.foreground);
    println!();
    
    // Demonstrate collaboration features
    println!("👥 Collaboration Features:");
    let collaboration = collaboration::CollaborationManager::new(&std::sync::Arc::new(config.clone())).await?;
    let sessions = collaboration.get_active_sessions().await?;
    println!("  - Active Sessions: {}", sessions.len());
    
    // Create a test session
    let session_id = collaboration.create_session(
        "Example Session",
        vec!["user1".to_string(), "user2".to_string()]
    ).await?;
    println!("  - Created Session: {}", session_id);
    println!();
    
    // Demonstrate text editor
    println!("📝 Text Editor:");
    let mut editor = editor::TextEditor::new().await?;
    editor.set_text("Hello, X Terminal!".to_string());
    println!("  - Text: {}", editor.text());
    
    editor.set_cursor_position(7);
    editor.insert_text("Advanced ")?;
    println!("  - After Edit: {}", editor.text());
    println!();
    
    // Demonstrate block management
    println!("📋 Block Management:");
    let mut block_manager = blocks::BlockManager::new();
    
    let block = blocks::Block {
        id: uuid::Uuid::new_v4(),
        command: "echo 'Hello from X Terminal!'".to_string(),
        output: "Hello from X Terminal!".to_string(),
        exit_code: Some(0),
        status: blocks::BlockStatus::Completed,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        working_directory: "/tmp".to_string(),
        environment: std::collections::HashMap::new(),
    };
    
    block_manager.add_block(block.clone()).await?;
    let blocks = block_manager.get_all_blocks();
    println!("  - Total Blocks: {}", blocks.len());
    println!("  - Last Command: {}", blocks[0].command);
    println!("  - Output: {}", blocks[0].output);
    println!();
    
    // Demonstrate shell integration
    println!("🐚 Shell Integration:");
    let shell_integration = core::shell::ShellIntegration::new(std::sync::Arc::new(config.clone())).await?;
    let detected_shell = shell_integration.detect_shell().await?;
    println!("  - Detected Shell: {:?}", detected_shell);
    println!();
    
    // Demonstrate voice interface (basic)
    println!("🎤 Voice Interface:");
    let voice_interface = ai::voice::VoiceInterface::new(std::sync::Arc::new(config.clone())).await?;
    println!("  - Voice Enabled: {}", voice_interface.is_enabled());
    
    if voice_interface.is_enabled() {
        let command = voice_interface.process_voice_command("list files").await?;
        println!("  - Voice Command Result: {:?}", command);
    }
    println!();
    
    // Demonstrate plugin system
    println!("🔌 Plugin System:");
    let mut plugin_manager = plugins::PluginManager::new().await?;
    plugin_manager.initialize().await?;
    
    let loaded_plugins = plugin_manager.get_loaded_plugins();
    println!("  - Loaded Plugins: {}", loaded_plugins.len());
    
    for plugin in loaded_plugins {
        println!("    - {}: {} v{}", plugin.id, plugin.name, plugin.version);
    }
    
    // Test plugin execution
    if !loaded_plugins.is_empty() {
        let result = plugin_manager.execute_plugin_command(
            "terminal-enhancer",
            "status",
            &[]
        ).await?;
        println!("  - Plugin Test Result: {}", result.message);
    }
    println!();
    
    // Demonstrate file operations
    println!("📁 File Operations:");
    let mut file_ops = tools::file_ops::FileOperations::new();
    
    // Create a test file
    let test_content = "This is a test file created by X Terminal!";
    file_ops.write_file("example_test.txt", test_content).await?;
    println!("  - Created file: example_test.txt");
    
    // Read the file back
    let read_content = file_ops.read_file("example_test.txt").await?;
    println!("  - File content: {}", read_content);
    
    // Clean up
    file_ops.delete_file("example_test.txt").await?;
    println!("  - Cleaned up test file");
    println!();
    
    // Demonstrate performance monitoring
    println!("📊 Performance Monitoring:");
    let mut perf_monitor = performance::PerformanceMonitor::new();
    perf_monitor.start_monitoring().await?;
    
    // Simulate some work
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    let metrics = perf_monitor.get_metrics().await?;
    println!("  - Frame Time: {:.2}ms", metrics.frame_time_ms);
    println!("  - FPS: {:.1}", metrics.fps);
    println!("  - Memory Usage: {:.2}MB", metrics.memory_usage_mb);
    println!();
    
    println!("🎉 X Terminal demonstration completed successfully!");
    println!("   All major components are working correctly.");
    println!();
    println!("💡 Next steps:");
    println!("   - Configure your AI providers in the config file");
    println!("   - Install additional plugins");
    println!("   - Customize your theme");
    println!("   - Set up collaboration with your team");
    
    Ok(())
}
