//! LLM provider implementations
//! 
//! This module implements support for multiple LLM providers including
//! OpenAI, Google Gemini, Anthropic Claude, Mistral AI, and DeepSeek.

use crate::config::ConfigManager;
use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// Provider manager for handling multiple LLM providers
pub struct ProviderManager {
    config: Arc<ConfigManager>,
    client: Client,
    providers: HashMap<String, Box<dyn LLMProvider + Send + Sync>>,
}

/// Trait for LLM providers
#[async_trait::async_trait]
pub trait LLMProvider {
    /// Provider name
    fn name(&self) -> &str;
    
    /// Generate completion
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse>;
    
    /// Check if provider is available
    async fn is_available(&self) -> bool;
    
    /// Get supported models
    fn supported_models(&self) -> Vec<String>;
}

/// Completion request
#[derive(Debug, Clone, Serialize)]
pub struct CompletionRequest {
    pub model: String,
    pub messages: Vec<Message>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub stream: bool,
}

/// Chat message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub role: String,
    pub content: String,
}

/// Completion response
#[derive(Debug, Clone, Deserialize)]
pub struct CompletionResponse {
    pub content: String,
    pub model: String,
    pub usage: Option<Usage>,
}

/// Token usage information
#[derive(Debug, Clone, Deserialize)]
pub struct Usage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

impl ProviderManager {
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let client = Client::new();
        let mut providers: HashMap<String, Box<dyn LLMProvider + Send + Sync>> = HashMap::new();
        
        // Initialize providers
        providers.insert("openai".to_string(), Box::new(OpenAIProvider::new(config, &client).await?));
        providers.insert("gemini".to_string(), Box::new(GeminiProvider::new(config, &client).await?));
        providers.insert("claude".to_string(), Box::new(ClaudeProvider::new(config, &client).await?));
        providers.insert("mistral".to_string(), Box::new(MistralProvider::new(config, &client).await?));
        providers.insert("deepseek".to_string(), Box::new(DeepSeekProvider::new(config, &client).await?));
        
        Ok(Self {
            config: config.clone(),
            client,
            providers,
        })
    }
    
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Initializing LLM providers...");
        
        for (name, provider) in &self.providers {
            if provider.is_available().await {
                tracing::info!("Provider '{}' is available", name);
            } else {
                tracing::warn!("Provider '{}' is not available", name);
            }
        }
        
        Ok(())
    }
    
    pub async fn complete(&self, provider_name: &str, request: &CompletionRequest) -> Result<CompletionResponse> {
        if let Some(provider) = self.providers.get(provider_name) {
            provider.complete(request).await
        } else {
            Err(anyhow::anyhow!("Provider '{}' not found", provider_name))
        }
    }
    
    pub fn get_available_providers(&self) -> Vec<String> {
        self.providers.keys().cloned().collect()
    }
}

/// OpenAI provider implementation
pub struct OpenAIProvider {
    api_key: String,
    client: Client,
    base_url: String,
}

#[async_trait::async_trait]
impl LLMProvider for OpenAIProvider {
    fn name(&self) -> &str {
        "openai"
    }
    
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse> {
        let url = format!("{}/chat/completions", self.base_url);

        let payload = serde_json::json!({
            "model": request.model,
            "messages": request.messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": request.stream
        });

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("OpenAI API error: {}", error_text));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json["choices"][0]["message"]["content"]
            .as_str()
            .unwrap_or("")
            .to_string();

        let usage = response_json.get("usage").map(|u| Usage {
            prompt_tokens: u["prompt_tokens"].as_u64().unwrap_or(0) as u32,
            completion_tokens: u["completion_tokens"].as_u64().unwrap_or(0) as u32,
            total_tokens: u["total_tokens"].as_u64().unwrap_or(0) as u32,
        });

        Ok(CompletionResponse {
            content,
            model: request.model.clone(),
            usage,
        })
    }
    
    async fn is_available(&self) -> bool {
        !self.api_key.is_empty()
    }
    
    fn supported_models(&self) -> Vec<String> {
        vec![
            "gpt-4".to_string(),
            "gpt-4-turbo".to_string(),
            "gpt-3.5-turbo".to_string(),
        ]
    }
}

impl OpenAIProvider {
    pub async fn new(config: &Arc<ConfigManager>, client: &Client) -> Result<Self> {
        let api_key = config.get_ai_api_key("openai").cloned().unwrap_or_default();
        
        Ok(Self {
            api_key,
            client: client.clone(),
            base_url: "https://api.openai.com/v1".to_string(),
        })
    }
}

/// Google Gemini provider implementation
pub struct GeminiProvider {
    api_key: String,
    client: Client,
    base_url: String,
}

#[async_trait::async_trait]
impl LLMProvider for GeminiProvider {
    fn name(&self) -> &str {
        "gemini"
    }
    
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse> {
        let url = format!("{}/v1beta/models/{}:generateContent", self.base_url, request.model);

        // Convert messages to Gemini format
        let contents = request.messages.iter().map(|msg| {
            serde_json::json!({
                "role": if msg.role == "assistant" { "model" } else { &msg.role },
                "parts": [{"text": msg.content}]
            })
        }).collect::<Vec<_>>();

        let payload = serde_json::json!({
            "contents": contents,
            "generationConfig": {
                "maxOutputTokens": request.max_tokens,
                "temperature": request.temperature
            }
        });

        let response = self.client
            .post(&url)
            .header("x-goog-api-key", &self.api_key)
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Gemini API error: {}", error_text));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json["candidates"][0]["content"]["parts"][0]["text"]
            .as_str()
            .unwrap_or("")
            .to_string();

        Ok(CompletionResponse {
            content,
            model: request.model.clone(),
            usage: None, // Gemini doesn't provide detailed usage in the same format
        })
    }
    
    async fn is_available(&self) -> bool {
        !self.api_key.is_empty()
    }
    
    fn supported_models(&self) -> Vec<String> {
        vec![
            "gemini-1.5-flash".to_string(),
            "gemini-1.5-flash-8b".to_string(),
        ]
    }
}

impl GeminiProvider {
    pub async fn new(config: &Arc<ConfigManager>, client: &Client) -> Result<Self> {
        let api_key = config.get_ai_api_key("gemini").cloned().unwrap_or_default();
        
        Ok(Self {
            api_key,
            client: client.clone(),
            base_url: "https://generativelanguage.googleapis.com/v1beta".to_string(),
        })
    }
}

/// Anthropic Claude provider implementation
pub struct ClaudeProvider {
    api_key: String,
    client: Client,
    base_url: String,
}

#[async_trait::async_trait]
impl LLMProvider for ClaudeProvider {
    fn name(&self) -> &str {
        "claude"
    }
    
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse> {
        let url = format!("{}/v1/messages", self.base_url);

        // Convert messages to Claude format
        let mut system_message = String::new();
        let mut user_messages = Vec::new();

        for message in &request.messages {
            match message.role.as_str() {
                "system" => system_message = message.content.clone(),
                "user" | "assistant" => user_messages.push(serde_json::json!({
                    "role": message.role,
                    "content": message.content
                })),
                _ => {}
            }
        }

        let mut payload = serde_json::json!({
            "model": request.model,
            "max_tokens": request.max_tokens.unwrap_or(1000),
            "messages": user_messages
        });

        if !system_message.is_empty() {
            payload["system"] = serde_json::Value::String(system_message);
        }

        let response = self.client
            .post(&url)
            .header("x-api-key", &self.api_key)
            .header("Content-Type", "application/json")
            .header("anthropic-version", "2023-06-01")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Claude API error: {}", error_text));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json["content"][0]["text"]
            .as_str()
            .unwrap_or("")
            .to_string();

        let usage = response_json.get("usage").map(|u| Usage {
            prompt_tokens: u["input_tokens"].as_u64().unwrap_or(0) as u32,
            completion_tokens: u["output_tokens"].as_u64().unwrap_or(0) as u32,
            total_tokens: (u["input_tokens"].as_u64().unwrap_or(0) + u["output_tokens"].as_u64().unwrap_or(0)) as u32,
        });

        Ok(CompletionResponse {
            content,
            model: request.model.clone(),
            usage,
        })
    }
    
    async fn is_available(&self) -> bool {
        !self.api_key.is_empty()
    }
    
    fn supported_models(&self) -> Vec<String> {
        vec![
            "claude-3-5-sonnet-20241022".to_string(),
            "claude-3-5-haiku-20241022".to_string(),
            "claude-3-opus-20240229".to_string(),
        ]
    }
}

impl ClaudeProvider {
    pub async fn new(config: &Arc<ConfigManager>, client: &Client) -> Result<Self> {
        let api_key = config.get_ai_api_key("claude").cloned().unwrap_or_default();
        
        Ok(Self {
            api_key,
            client: client.clone(),
            base_url: "https://api.anthropic.com/v1".to_string(),
        })
    }
}

/// Mistral AI provider implementation
pub struct MistralProvider {
    api_key: String,
    client: Client,
    base_url: String,
}

#[async_trait::async_trait]
impl LLMProvider for MistralProvider {
    fn name(&self) -> &str {
        "mistral"
    }
    
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse> {
        let url = format!("{}/v1/chat/completions", self.base_url);

        let payload = serde_json::json!({
            "model": request.model,
            "messages": request.messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": request.stream
        });

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Mistral API error: {}", error_text));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json["choices"][0]["message"]["content"]
            .as_str()
            .unwrap_or("")
            .to_string();

        let usage = response_json.get("usage").map(|u| Usage {
            prompt_tokens: u["prompt_tokens"].as_u64().unwrap_or(0) as u32,
            completion_tokens: u["completion_tokens"].as_u64().unwrap_or(0) as u32,
            total_tokens: u["total_tokens"].as_u64().unwrap_or(0) as u32,
        });

        Ok(CompletionResponse {
            content,
            model: request.model.clone(),
            usage,
        })
    }
    
    async fn is_available(&self) -> bool {
        !self.api_key.is_empty()
    }
    
    fn supported_models(&self) -> Vec<String> {
        vec![
            "mistral-large-latest".to_string(),
            "mistral-small-latest".to_string(),
        ]
    }
}

impl MistralProvider {
    pub async fn new(config: &Arc<ConfigManager>, client: &Client) -> Result<Self> {
        let api_key = config.get_ai_api_key("mistral").cloned().unwrap_or_default();
        
        Ok(Self {
            api_key,
            client: client.clone(),
            base_url: "https://api.mistral.ai/v1".to_string(),
        })
    }
}

/// DeepSeek provider implementation
pub struct DeepSeekProvider {
    api_key: String,
    client: Client,
    base_url: String,
}

#[async_trait::async_trait]
impl LLMProvider for DeepSeekProvider {
    fn name(&self) -> &str {
        "deepseek"
    }
    
    async fn complete(&self, request: &CompletionRequest) -> Result<CompletionResponse> {
        let url = format!("{}/v1/chat/completions", self.base_url);

        let payload = serde_json::json!({
            "model": request.model,
            "messages": request.messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": request.stream
        });

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("DeepSeek API error: {}", error_text));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json["choices"][0]["message"]["content"]
            .as_str()
            .unwrap_or("")
            .to_string();

        let usage = response_json.get("usage").map(|u| Usage {
            prompt_tokens: u["prompt_tokens"].as_u64().unwrap_or(0) as u32,
            completion_tokens: u["completion_tokens"].as_u64().unwrap_or(0) as u32,
            total_tokens: u["total_tokens"].as_u64().unwrap_or(0) as u32,
        });

        Ok(CompletionResponse {
            content,
            model: request.model.clone(),
            usage,
        })
    }
    
    async fn is_available(&self) -> bool {
        !self.api_key.is_empty()
    }
    
    fn supported_models(&self) -> Vec<String> {
        vec![
            "deepseek-chat".to_string(),
            "deepseek-coder".to_string(),
        ]
    }
}

impl DeepSeekProvider {
    pub async fn new(config: &Arc<ConfigManager>, client: &Client) -> Result<Self> {
        let api_key = config.get_ai_api_key("deepseek").cloned().unwrap_or_default();
        
        Ok(Self {
            api_key,
            client: client.clone(),
            base_url: "https://api.deepseek.com/v1".to_string(),
        })
    }
}

/// Provider errors
#[derive(thiserror::Error, Debug)]
pub enum ProviderError {
    #[error("API error: {0}")]
    Api(String),
    
    #[error("Authentication error: {0}")]
    Auth(String),
    
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Other error: {0}")]
    Other(#[from] anyhow::Error),
}
