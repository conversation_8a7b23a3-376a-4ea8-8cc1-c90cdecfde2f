//! Command suggestion engine
//! 
//! This module implements intelligent command suggestions based on context,
//! history, and AI analysis.

use crate::config::ConfigManager;
use super::providers::{ProviderManager, CompletionRequest, Message};
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Suggestion engine for command completions and suggestions
pub struct SuggestionEngine {
    config: Arc<ConfigManager>,
    providers: Arc<RwLock<ProviderManager>>,
    command_history: Vec<String>,
    context_cache: std::collections::HashMap<String, Vec<String>>,
}

impl SuggestionEngine {
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let providers = Arc::new(RwLock::new(ProviderManager::new(config).await?));
        
        Ok(Self {
            config: config.clone(),
            providers,
            command_history: Vec::new(),
            context_cache: std::collections::HashMap::new(),
        })
    }
    
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Initializing suggestion engine...");
        
        // Load command history
        self.load_command_history().await?;
        
        tracing::info!("Suggestion engine initialized successfully");
        Ok(())
    }
    
    pub async fn process_pending(&mut self) -> Result<()> {
        // Process any pending suggestion requests
        Ok(())
    }
    
    pub async fn get_command_suggestions(&self, context: &str) -> Result<Vec<String>> {
        // Check cache first
        if let Some(cached) = self.context_cache.get(context) {
            return Ok(cached.clone());
        }
        
        let request = CompletionRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![
                Message {
                    role: "system".to_string(),
                    content: "You are a command-line assistant. Suggest relevant shell commands based on the user's context. Return suggestions as a JSON array of strings.".to_string(),
                },
                Message {
                    role: "user".to_string(),
                    content: format!("Context: {}\nRecent commands: {:?}", context, self.command_history.iter().rev().take(5).collect::<Vec<_>>()),
                },
            ],
            max_tokens: Some(500),
            temperature: Some(0.3),
            stream: false,
        };
        
        let providers = self.providers.read().await;
        let response = providers.complete("openai", &request).await?;
        
        // Try to parse as JSON array, fallback to single suggestion
        let suggestions = match serde_json::from_str::<Vec<String>>(&response.content) {
            Ok(suggestions) => suggestions,
            Err(_) => vec![response.content],
        };
        
        Ok(suggestions)
    }
    
    async fn load_command_history(&mut self) -> Result<()> {
        tracing::info!("Loading command history...");

        // Try to load from history file
        let history_path = self.get_history_file_path()?;

        if history_path.exists() {
            let content = tokio::fs::read_to_string(&history_path).await?;

            for line in content.lines() {
                let line = line.trim();
                if !line.is_empty() && !line.starts_with('#') {
                    self.command_history.push(line.to_string());
                }
            }

            tracing::info!("Loaded {} commands from history", self.command_history.len());
        } else {
            // Create default history with common commands
            self.create_default_history();
            tracing::info!("Created default command history");
        }

        Ok(())
    }

    /// Get the path to the command history file
    fn get_history_file_path(&self) -> Result<std::path::PathBuf> {
        let home_dir = std::env::var("HOME")
            .or_else(|_| std::env::var("USERPROFILE"))
            .map_err(|_| anyhow::anyhow!("Could not determine home directory"))?;

        let history_path = std::path::Path::new(&home_dir)
            .join(".x_terminal")
            .join("command_history.txt");

        // Create directory if it doesn't exist
        if let Some(parent) = history_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        Ok(history_path)
    }

    /// Create default command history with common commands
    fn create_default_history(&mut self) {
        let default_commands = vec![
            "ls -la".to_string(),
            "cd ..".to_string(),
            "pwd".to_string(),
            "git status".to_string(),
            "git add .".to_string(),
            "git commit -m".to_string(),
            "git push".to_string(),
            "git pull".to_string(),
            "npm install".to_string(),
            "npm start".to_string(),
            "cargo build".to_string(),
            "cargo test".to_string(),
            "cargo run".to_string(),
            "python -m pip install".to_string(),
            "python main.py".to_string(),
            "docker build".to_string(),
            "docker run".to_string(),
            "docker ps".to_string(),
            "grep -r".to_string(),
            "find . -name".to_string(),
            "chmod +x".to_string(),
            "sudo apt update".to_string(),
            "brew install".to_string(),
            "code .".to_string(),
            "vim".to_string(),
            "cat".to_string(),
            "tail -f".to_string(),
            "ps aux".to_string(),
            "kill -9".to_string(),
            "df -h".to_string(),
            "free -h".to_string(),
            "top".to_string(),
            "htop".to_string(),
        ];

        self.command_history.extend(default_commands);
    }

    /// Save command history to file
    pub async fn save_command_history(&self) -> Result<()> {
        let history_path = self.get_history_file_path()?;

        let content = self.command_history.join("\n");
        tokio::fs::write(&history_path, content).await?;

        tracing::info!("Saved {} commands to history file", self.command_history.len());
        Ok(())
    }
    
    pub fn add_command_to_history(&mut self, command: String) {
        self.command_history.push(command);
        
        // Keep only recent commands
        if self.command_history.len() > 1000 {
            self.command_history.drain(0..100);
        }
    }
}
