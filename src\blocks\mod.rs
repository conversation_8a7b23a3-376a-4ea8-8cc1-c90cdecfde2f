//! Block management system
//! 
//! This module implements the block-based interface that groups commands
//! and their outputs, similar to <PERSON><PERSON>'s block system.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Block manager for handling command blocks
pub struct BlockManager {
    /// Active blocks
    blocks: HashMap<Uuid, Block>,
    /// Block history
    history: Vec<Uuid>,
    /// Current active block
    current_block: Option<Uuid>,
    /// Block listeners
    listeners: Vec<Box<dyn BlockListener>>,
    /// Maximum number of blocks to keep in history
    max_history: usize,
}

/// A command block containing command and output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Block {
    /// Block ID
    pub id: Uuid,
    /// Command text
    pub command: String,
    /// Command output
    pub output: String,
    /// Block status
    pub status: BlockStatus,
    /// Start time
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// End time
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// Exit code
    pub exit_code: Option<i32>,
    /// Working directory
    pub working_dir: String,
    /// Environment variables
    pub env_vars: HashMap<String, String>,
    /// Block metadata
    pub metadata: BlockMetadata,
}

/// Block status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BlockStatus {
    /// Command is running
    Running,
    /// Command completed successfully
    Success,
    /// Command failed
    Failed,
    /// Command was cancelled
    Cancelled,
}

/// Block metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockMetadata {
    /// AI suggestions for this block
    pub ai_suggestions: Vec<String>,
    /// User notes
    pub notes: String,
    /// Tags
    pub tags: Vec<String>,
    /// Bookmarked
    pub bookmarked: bool,
}

/// Block event listener trait
pub trait BlockListener {
    /// Called when a new block is created
    fn on_block_created(&self, id: Uuid, block: &Block) -> Result<()>;

    /// Called when a block is updated
    fn on_block_updated(&self, id: Uuid, block: &Block) -> Result<()>;

    /// Called when a block is deleted
    fn on_block_deleted(&self, id: Uuid) -> Result<()>;

    /// Called when a block status changes
    fn on_block_status_changed(&self, id: Uuid, old_status: BlockStatus, new_status: BlockStatus) -> Result<()>;
}

impl Default for BlockMetadata {
    fn default() -> Self {
        Self {
            ai_suggestions: Vec::new(),
            notes: String::new(),
            tags: Vec::new(),
            bookmarked: false,
        }
    }
}

impl BlockManager {
    /// Create a new block manager
    pub fn new() -> Self {
        Self {
            blocks: HashMap::new(),
            history: Vec::new(),
            current_block: None,
            listeners: Vec::new(),
            max_history: 1000, // Keep last 1000 blocks
        }
    }
    
    /// Create a new block
    pub fn create_block(&mut self, command: String, working_dir: String) -> Uuid {
        let id = Uuid::new_v4();
        let block = Block {
            id,
            command,
            output: String::new(),
            status: BlockStatus::Running,
            start_time: chrono::Utc::now(),
            end_time: None,
            exit_code: None,
            working_dir,
            env_vars: HashMap::new(),
            metadata: BlockMetadata::default(),
        };
        
        let block_clone = block.clone();
        self.blocks.insert(id, block);
        self.history.push(id);
        self.current_block = Some(id);

        // Notify listeners
        for listener in &self.listeners {
            if let Err(e) = listener.on_block_created(id, &block_clone) {
                tracing::error!("Error notifying listener: {}", e);
            }
        }

        tracing::info!("Created new block: {}", id);
        id
    }
    
    /// Update block output
    pub fn update_block_output(&mut self, block_id: Uuid, output: &str) -> Result<()> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            block.output.push_str(output);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Complete a block
    pub fn complete_block(&mut self, block_id: Uuid, exit_code: i32) -> Result<()> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            block.status = if exit_code == 0 {
                BlockStatus::Success
            } else {
                BlockStatus::Failed
            };
            block.exit_code = Some(exit_code);
            block.end_time = Some(chrono::Utc::now());
            
            if self.current_block == Some(block_id) {
                self.current_block = None;
            }
            
            tracing::info!("Completed block: {} with exit code: {}", block_id, exit_code);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Get a block by ID
    pub fn get_block(&self, block_id: Uuid) -> Option<&Block> {
        self.blocks.get(&block_id)
    }
    
    /// Get current active block
    pub fn get_current_block(&self) -> Option<&Block> {
        self.current_block.and_then(|id| self.blocks.get(&id))
    }
    
    /// Get all blocks
    pub fn get_all_blocks(&self) -> Vec<&Block> {
        self.history.iter()
            .filter_map(|id| self.blocks.get(id))
            .collect()
    }
    
    /// Search blocks by command or output
    pub fn search_blocks(&self, query: &str) -> Vec<&Block> {
        self.blocks.values()
            .filter(|block| {
                block.command.contains(query) || 
                block.output.contains(query) ||
                block.metadata.notes.contains(query) ||
                block.metadata.tags.iter().any(|tag| tag.contains(query))
            })
            .collect()
    }
    
    /// Get blocks by status
    pub fn get_blocks_by_status(&self, status: BlockStatus) -> Vec<&Block> {
        self.blocks.values()
            .filter(|block| block.status == status)
            .collect()
    }
    
    /// Add AI suggestion to block
    pub fn add_ai_suggestion(&mut self, block_id: Uuid, suggestion: String) -> Result<()> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            block.metadata.ai_suggestions.push(suggestion);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Add note to block
    pub fn add_note(&mut self, block_id: Uuid, note: String) -> Result<()> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            block.metadata.notes = note;
            Ok(())
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Add tag to block
    pub fn add_tag(&mut self, block_id: Uuid, tag: String) -> Result<()> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            if !block.metadata.tags.contains(&tag) {
                block.metadata.tags.push(tag);
            }
            Ok(())
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Toggle bookmark on block
    pub fn toggle_bookmark(&mut self, block_id: Uuid) -> Result<bool> {
        if let Some(block) = self.blocks.get_mut(&block_id) {
            block.metadata.bookmarked = !block.metadata.bookmarked;
            Ok(block.metadata.bookmarked)
        } else {
            Err(anyhow::anyhow!("Block not found: {}", block_id))
        }
    }
    
    /// Get bookmarked blocks
    pub fn get_bookmarked_blocks(&self) -> Vec<&Block> {
        self.blocks.values()
            .filter(|block| block.metadata.bookmarked)
            .collect()
    }
    
    /// Clear old blocks (keep only recent ones)
    pub fn cleanup_old_blocks(&mut self, keep_count: usize) {
        if self.history.len() > keep_count {
            let to_remove = self.history.len() - keep_count;
            let removed_ids: Vec<Uuid> = self.history.drain(0..to_remove).collect();
            
            for id in removed_ids {
                self.blocks.remove(&id);
            }
            
            tracing::info!("Cleaned up {} old blocks", to_remove);
        }
    }
    
    /// Export blocks to JSON
    pub fn export_blocks(&self) -> Result<String> {
        let blocks: Vec<&Block> = self.get_all_blocks();
        let json = serde_json::to_string_pretty(&blocks)?;
        Ok(json)
    }
    
    /// Import blocks from JSON
    pub fn import_blocks(&mut self, json: &str) -> Result<usize> {
        let blocks: Vec<Block> = serde_json::from_str(json)?;
        let count = blocks.len();
        
        for block in blocks {
            self.blocks.insert(block.id, block.clone());
            if !self.history.contains(&block.id) {
                self.history.push(block.id);
            }
        }
        
        tracing::info!("Imported {} blocks", count);
        Ok(count)
    }

    /// Add a block listener
    pub fn add_listener(&mut self, listener: Box<dyn BlockListener>) {
        self.listeners.push(listener);
    }

    /// Remove a block listener
    pub fn remove_listener(&mut self, index: usize) {
        if index < self.listeners.len() {
            self.listeners.remove(index);
        }
    }

    /// Set maximum history size
    pub fn set_max_history(&mut self, max_history: usize) {
        self.max_history = max_history;
        self.trim_history();
    }

    /// Trim history to max size
    fn trim_history(&mut self) {
        if self.history.len() > self.max_history {
            // Keep only the most recent blocks
            let to_remove = self.history.len() - self.max_history;
            let removed_ids: Vec<Uuid> = self.history.drain(0..to_remove).collect();

            // Remove blocks that are no longer in history
            for id in removed_ids {
                // Only remove if not current and not bookmarked
                if Some(id) != self.current_block {
                    if let Some(block) = self.blocks.get(&id) {
                        if !block.metadata.bookmarked {
                            self.blocks.remove(&id);
                        }
                    }
                }
            }
        }
    }
}
