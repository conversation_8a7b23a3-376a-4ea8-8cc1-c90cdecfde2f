//! Terminal event handling system
//! 
//! This module defines the event system for handling terminal events,
//! user input, and system notifications.

use super::{TerminalError, TerminalResult};
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use uuid::Uuid;

/// Terminal event types
#[derive(Debug, Clone)]
pub enum TerminalEvent {
    /// Input from user
    Input(InputEvent),
    /// Output from shell/process
    Output(OutputEvent),
    /// Resize event
    Resize(ResizeEvent),
    /// Shell hook event
    ShellHook(ShellHookEvent),
    /// AI event
    AI(AIEvent),
    /// System event
    System(SystemEvent),
}

/// Input events from user
#[derive(Debug, Clone)]
pub struct InputEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub input_type: InputType,
    pub data: Vec<u8>,
}

/// Types of input
#[derive(Debug, <PERSON>lone)]
pub enum InputType {
    /// Keyboard input
    Keyboard,
    /// Mouse input
    Mouse,
    /// Paste operation
    Paste,
    /// Voice input
    Voice,
}

/// Output events from shell
#[derive(Debug, <PERSON>lone)]
pub struct OutputEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub data: Vec<u8>,
    pub source: OutputSource,
}

/// Source of output
#[derive(Debug, Clone)]
pub enum OutputSource {
    /// Standard output
    Stdout,
    /// Standard error
    Stderr,
    /// System message
    System,
}

/// Resize events
#[derive(Debug, Clone)]
pub struct ResizeEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub rows: u16,
    pub cols: u16,
    pub pixel_width: u16,
    pub pixel_height: u16,
}

/// Shell hook events
#[derive(Debug, Clone)]
pub struct ShellHookEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub hook_type: String,
    pub data: std::collections::HashMap<String, String>,
}

/// AI-related events
#[derive(Debug, Clone)]
pub struct AIEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub event_type: AIEventType,
    pub data: serde_json::Value,
}

/// AI event types
#[derive(Debug, Clone)]
pub enum AIEventType {
    /// Command suggestion
    Suggestion,
    /// Command completion
    Completion,
    /// Error analysis
    ErrorAnalysis,
    /// Code generation
    CodeGeneration,
    /// Voice command
    VoiceCommand,
}

/// System events
#[derive(Debug, Clone)]
pub struct SystemEvent {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub event_type: SystemEventType,
    pub message: String,
}

/// System event types
#[derive(Debug, Clone)]
pub enum SystemEventType {
    /// Application startup
    Startup,
    /// Application shutdown
    Shutdown,
    /// Configuration change
    ConfigChange,
    /// Theme change
    ThemeChange,
    /// Plugin loaded
    PluginLoaded,
    /// Error occurred
    Error,
    /// Warning
    Warning,
    /// Information
    Info,
}

/// Event handler for managing terminal events
pub struct EventHandler {
    /// Event sender
    sender: mpsc::UnboundedSender<TerminalEvent>,
    /// Event receiver
    receiver: Arc<RwLock<mpsc::UnboundedReceiver<TerminalEvent>>>,
    /// Event listeners
    listeners: Arc<RwLock<Vec<Box<dyn EventListener + Send + Sync>>>>,
}

/// Trait for event listeners
pub trait EventListener {
    /// Handle a terminal event
    fn handle_event(&mut self, event: &TerminalEvent) -> TerminalResult<()>;
    
    /// Get listener name
    fn name(&self) -> &str;
    
    /// Check if listener is interested in this event type
    fn interested_in(&self, event: &TerminalEvent) -> bool;
}

impl EventHandler {
    /// Create a new event handler
    pub fn new() -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        Self {
            sender,
            receiver: Arc::new(RwLock::new(receiver)),
            listeners: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// Send an event
    pub async fn send_event(&self, event: TerminalEvent) -> TerminalResult<()> {
        self.sender.send(event)
            .map_err(|e| TerminalError::Event(format!("Failed to send event: {}", e)))?;
        Ok(())
    }
    
    /// Add an event listener
    pub async fn add_listener(&self, listener: Box<dyn EventListener + Send + Sync>) {
        let mut listeners = self.listeners.write().await;
        listeners.push(listener);
    }
    
    /// Remove an event listener by name
    pub async fn remove_listener(&self, name: &str) {
        let mut listeners = self.listeners.write().await;
        listeners.retain(|l| l.name() != name);
    }
    
    /// Start processing events
    pub async fn start_processing(&self) -> TerminalResult<()> {
        let receiver = self.receiver.clone();
        let listeners = self.listeners.clone();
        
        tokio::spawn(async move {
            let mut receiver = receiver.write().await;
            
            while let Some(event) = receiver.recv().await {
                let listeners = listeners.read().await;
                
                // For now, just log events since we can't easily make listeners mutable
                // In a real implementation, we'd use Arc<Mutex<dyn EventListener>>
                for listener in listeners.iter() {
                    if listener.interested_in(&event) {
                        tracing::debug!("Event listener '{}' would handle event", listener.name());
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Create an input event
    pub fn create_input_event(&self, input_type: InputType, data: Vec<u8>) -> TerminalEvent {
        TerminalEvent::Input(InputEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            input_type,
            data,
        })
    }
    
    /// Create an output event
    pub fn create_output_event(&self, source: OutputSource, data: Vec<u8>) -> TerminalEvent {
        TerminalEvent::Output(OutputEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            data,
            source,
        })
    }
    
    /// Create a resize event
    pub fn create_resize_event(&self, rows: u16, cols: u16, pixel_width: u16, pixel_height: u16) -> TerminalEvent {
        TerminalEvent::Resize(ResizeEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            rows,
            cols,
            pixel_width,
            pixel_height,
        })
    }
    
    /// Create a shell hook event
    pub fn create_shell_hook_event(&self, hook_type: String, data: std::collections::HashMap<String, String>) -> TerminalEvent {
        TerminalEvent::ShellHook(ShellHookEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            hook_type,
            data,
        })
    }
    
    /// Create an AI event
    pub fn create_ai_event(&self, event_type: AIEventType, data: serde_json::Value) -> TerminalEvent {
        TerminalEvent::AI(AIEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            event_type,
            data,
        })
    }
    
    /// Create a system event
    pub fn create_system_event(&self, event_type: SystemEventType, message: String) -> TerminalEvent {
        TerminalEvent::System(SystemEvent {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            event_type,
            message,
        })
    }

    /// Send multiple events in batch
    pub async fn send_events_batch(&self, events: Vec<TerminalEvent>) -> TerminalResult<()> {
        for event in events {
            self.send_event(event).await?;
        }
        Ok(())
    }

    /// Get event statistics
    pub async fn get_event_stats(&self) -> EventStats {
        // In a real implementation, this would track actual statistics
        EventStats {
            total_events: 0,
            events_per_second: 0.0,
            listeners_count: self.listeners.read().await.len(),
        }
    }

    /// Clear all listeners
    pub async fn clear_listeners(&self) {
        let mut listeners = self.listeners.write().await;
        listeners.clear();
    }
}

/// Event statistics
#[derive(Debug, Clone)]
pub struct EventStats {
    pub total_events: u64,
    pub events_per_second: f64,
    pub listeners_count: usize,
}

/// Event filter for selective event processing
pub trait EventFilter {
    /// Check if event should be processed
    fn should_process(&self, event: &TerminalEvent) -> bool;

    /// Get filter name
    fn name(&self) -> &str;
}

/// Event filter that only processes specific event types
pub struct TypeEventFilter {
    name: String,
    allowed_types: Vec<std::mem::Discriminant<TerminalEvent>>,
}

impl TypeEventFilter {
    pub fn new(name: String, allowed_types: Vec<TerminalEvent>) -> Self {
        let discriminants = allowed_types.into_iter()
            .map(|event| std::mem::discriminant(&event))
            .collect();

        Self {
            name,
            allowed_types: discriminants,
        }
    }
}

impl EventFilter for TypeEventFilter {
    fn should_process(&self, event: &TerminalEvent) -> bool {
        let event_discriminant = std::mem::discriminant(event);
        self.allowed_types.contains(&event_discriminant)
    }

    fn name(&self) -> &str {
        &self.name
    }
}

/// Event filter that processes events based on time window
pub struct TimeWindowEventFilter {
    name: String,
    window_start: chrono::DateTime<chrono::Utc>,
    window_end: chrono::DateTime<chrono::Utc>,
}

impl TimeWindowEventFilter {
    pub fn new(name: String, window_start: chrono::DateTime<chrono::Utc>, window_end: chrono::DateTime<chrono::Utc>) -> Self {
        Self {
            name,
            window_start,
            window_end,
        }
    }
}

impl EventFilter for TimeWindowEventFilter {
    fn should_process(&self, event: &TerminalEvent) -> bool {
        let timestamp = match event {
            TerminalEvent::Input(e) => e.timestamp,
            TerminalEvent::Output(e) => e.timestamp,
            TerminalEvent::Resize(e) => e.timestamp,
            TerminalEvent::ShellHook(e) => e.timestamp,
            TerminalEvent::AI(e) => e.timestamp,
            TerminalEvent::System(e) => e.timestamp,
        };

        timestamp >= self.window_start && timestamp <= self.window_end
    }

    fn name(&self) -> &str {
        &self.name
    }
}

/// Default event listener for logging
pub struct LoggingEventListener {
    name: String,
}

impl LoggingEventListener {
    pub fn new() -> Self {
        Self {
            name: "logging".to_string(),
        }
    }
}

impl EventListener for LoggingEventListener {
    fn handle_event(&mut self, event: &TerminalEvent) -> TerminalResult<()> {
        match event {
            TerminalEvent::Input(input) => {
                tracing::debug!("Input event: {:?} ({} bytes)", input.input_type, input.data.len());
            }
            TerminalEvent::Output(output) => {
                tracing::debug!("Output event: {:?} ({} bytes)", output.source, output.data.len());
            }
            TerminalEvent::Resize(resize) => {
                tracing::info!("Resize event: {}x{} ({}x{} pixels)", 
                             resize.cols, resize.rows, resize.pixel_width, resize.pixel_height);
            }
            TerminalEvent::ShellHook(hook) => {
                tracing::info!("Shell hook event: {} with {} data items", 
                             hook.hook_type, hook.data.len());
            }
            TerminalEvent::AI(ai) => {
                tracing::info!("AI event: {:?}", ai.event_type);
            }
            TerminalEvent::System(system) => {
                match system.event_type {
                    SystemEventType::Error => tracing::error!("System: {}", system.message),
                    SystemEventType::Warning => tracing::warn!("System: {}", system.message),
                    _ => tracing::info!("System: {}", system.message),
                }
            }
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn interested_in(&self, _event: &TerminalEvent) -> bool {
        true // Log all events
    }
}
