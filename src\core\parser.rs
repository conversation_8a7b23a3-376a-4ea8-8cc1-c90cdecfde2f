//! VT100/ANSI escape sequence parser
//! 
//! This module implements a VT100-compatible parser for handling ANSI escape sequences
//! and converting them into terminal actions.

use super::TerminalResult;
use super::terminal::{ParserAction, Color, CellAttributes, HyperlinkInfo, CommandState};
use vte::{Parser, Perform};
use std::collections::HashMap;

/// VT100 parser for ANSI escape sequences
pub struct VT100Parser {
    /// VTE parser
    parser: Parser,
    /// Collected actions
    actions: Vec<ParserAction>,
    /// Current parser state
    state: ParserState,
}

/// Parser performer that implements the VTE Perform trait
struct ParserPerformer<'a> {
    actions: &'a mut Vec<ParserAction>,
    state: &'a mut ParserState,
}

/// Parser state for tracking context
#[derive(Debug, Default)]
struct ParserState {
    /// Current foreground color
    fg_color: Color,
    /// Current background color
    bg_color: Color,
    /// Current cell attributes
    attributes: CellAttributes,
    /// Cursor position
    cursor_row: usize,
    cursor_col: usize,
    /// Window title
    window_title: Option<String>,
    /// Icon name
    icon_name: Option<String>,
    /// Color palette
    color_palette: HashMap<u8, Color>,
    /// Current working directory
    current_directory: Option<String>,
    /// Current hyperlink
    current_hyperlink: Option<HyperlinkInfo>,
    /// Clipboard content
    clipboard_content: Option<String>,
    /// Command execution state
    command_state: CommandState,
}

impl VT100Parser {
    /// Create a new VT100 parser
    pub fn new() -> Self {
        Self {
            parser: Parser::new(),
            actions: Vec::new(),
            state: ParserState::default(),
        }
    }
    
    /// Parse input data and return actions
    pub fn parse(&mut self, data: &[u8]) -> TerminalResult<Vec<ParserAction>> {
        self.actions.clear();

        for &byte in data {
            let mut performer = ParserPerformer {
                actions: &mut self.actions,
                state: &mut self.state,
            };
            self.parser.advance(&mut performer, byte);
        }

        Ok(std::mem::take(&mut self.actions))
    }
    

}

impl<'a> ParserPerformer<'a> {
    /// Add an action to the list
    fn add_action(&mut self, action: ParserAction) {
        self.actions.push(action);
    }

    /// Handle SGR (Set Graphics Rendition) sequences
    fn handle_sgr_params(&mut self, params: &vte::Params) {
        for param in params.iter() {
            for &value in param {
                match value {
                    0 => {
                        // Reset all attributes
                        self.state.attributes = CellAttributes::default();
                        self.state.fg_color = Color::Default;
                        self.state.bg_color = Color::Default;
                    }
                    1 => self.state.attributes.bold = true,
                    2 => self.state.attributes.dim = true,
                    3 => self.state.attributes.italic = true,
                    4 => self.state.attributes.underline = true,
                    5 => self.state.attributes.blink = true,
                    7 => self.state.attributes.reverse = true,
                    9 => self.state.attributes.strikethrough = true,
                    22 => {
                        self.state.attributes.bold = false;
                        self.state.attributes.dim = false;
                    }
                    23 => self.state.attributes.italic = false,
                    24 => self.state.attributes.underline = false,
                    25 => self.state.attributes.blink = false,
                    27 => self.state.attributes.reverse = false,
                    29 => self.state.attributes.strikethrough = false,
                    30..=37 => {
                        // Foreground colors
                        self.state.fg_color = Color::Named((value - 30) as u8);
                    }
                    38 => {
                        // Extended foreground color (256-color or RGB)
                        // TODO: Handle extended color sequences
                    }
                    39 => {
                        // Default foreground color
                        self.state.fg_color = Color::Default;
                    }
                    40..=47 => {
                        // Background colors
                        self.state.bg_color = Color::Named((value - 40) as u8);
                    }
                    48 => {
                        // Extended background color (256-color or RGB)
                        // TODO: Handle extended color sequences
                    }
                    49 => {
                        // Default background color
                        self.state.bg_color = Color::Default;
                    }
                    90..=97 => {
                        // Bright foreground colors
                        self.state.fg_color = Color::Named((value - 90 + 8) as u8);
                    }
                    100..=107 => {
                        // Bright background colors
                        self.state.bg_color = Color::Named((value - 100 + 8) as u8);
                    }
                    _ => {
                        tracing::debug!("Unhandled SGR parameter: {}", value);
                    }
                }
            }
        }

        // Apply the current colors and attributes
        self.add_action(ParserAction::SetColor(self.state.fg_color, self.state.bg_color));
        self.add_action(ParserAction::SetAttributes(self.state.attributes.clone()));
    }
}

impl<'a> Perform for ParserPerformer<'a> {
    /// Handle printable characters
    fn print(&mut self, c: char) {
        self.add_action(ParserAction::Print(c));
    }
    
    /// Handle control characters
    fn execute(&mut self, byte: u8) {
        match byte {
            b'\n' => {
                // Line feed - move cursor down
                self.state.cursor_row += 1;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\r' => {
                // Carriage return - move cursor to beginning of line
                self.state.cursor_col = 0;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\t' => {
                // Tab - move cursor to next tab stop (every 8 columns)
                self.state.cursor_col = (self.state.cursor_col + 8) & !7;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'\x08' => {
                // Backspace - move cursor left
                if self.state.cursor_col > 0 {
                    self.state.cursor_col -= 1;
                    self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
                }
            }
            b'\x07' => {
                // Bell - could trigger notification
                // TODO: Implement bell handling
            }
            _ => {
                // Other control characters - mostly ignored for now
            }
        }
    }
    
    /// Handle escape sequences
    fn csi_dispatch(&mut self, params: &vte::Params, _intermediates: &[u8], ignore: bool, c: char) {
        if ignore {
            return;
        }
        
        match c {
            'A' => {
                // Cursor up
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                if self.state.cursor_row >= n {
                    self.state.cursor_row -= n;
                } else {
                    self.state.cursor_row = 0;
                }
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'B' => {
                // Cursor down
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_row += n;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'C' => {
                // Cursor right
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_col += n;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'D' => {
                // Cursor left
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                if self.state.cursor_col >= n {
                    self.state.cursor_col -= n;
                } else {
                    self.state.cursor_col = 0;
                }
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'H' | 'f' => {
                // Cursor position
                let row = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                let col = params.iter().nth(1).and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.state.cursor_row = row.saturating_sub(1);
                self.state.cursor_col = col.saturating_sub(1);
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            'J' => {
                // Erase in display
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(0);
                match n {
                    0 => {
                        // Clear from cursor to end of screen
                        // TODO: Implement partial clear
                    }
                    1 => {
                        // Clear from beginning of screen to cursor
                        // TODO: Implement partial clear
                    }
                    2 => {
                        // Clear entire screen
                        self.add_action(ParserAction::ClearScreen);
                    }
                    _ => {}
                }
            }
            'K' => {
                // Erase in line
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(0);
                match n {
                    0 => {
                        // Clear from cursor to end of line
                        // TODO: Implement partial line clear
                    }
                    1 => {
                        // Clear from beginning of line to cursor
                        // TODO: Implement partial line clear
                    }
                    2 => {
                        // Clear entire line
                        self.add_action(ParserAction::ClearLine);
                    }
                    _ => {}
                }
            }
            'm' => {
                // Set graphics rendition (colors and attributes)
                self.handle_sgr_params(params);
            }
            'S' => {
                // Scroll up
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.add_action(ParserAction::ScrollUp(n));
            }
            'T' => {
                // Scroll down
                let n = params.iter().next().and_then(|p| p.first()).copied().unwrap_or(1) as usize;
                self.add_action(ParserAction::ScrollDown(n));
            }
            _ => {
                // Unhandled CSI sequence
                tracing::debug!("Unhandled CSI sequence: {} with params {:?}", c, params);
            }
        }
    }

    /// Handle other escape sequences (mostly unimplemented for now)
    fn esc_dispatch(&mut self, intermediates: &[u8], ignore: bool, byte: u8) {
        if ignore {
            return;
        }
        
        match byte {
            b'D' => {
                // Index - move cursor down one line
                self.state.cursor_row += 1;
                self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
            }
            b'M' => {
                // Reverse index - move cursor up one line
                if self.state.cursor_row > 0 {
                    self.state.cursor_row -= 1;
                    self.add_action(ParserAction::MoveCursor(self.state.cursor_row, self.state.cursor_col));
                }
            }
            b'c' => {
                // Reset terminal
                *self.state = ParserState::default();
                self.add_action(ParserAction::ClearScreen);
            }
            _ => {
                tracing::debug!("Unhandled ESC sequence: {} with intermediates {:?}", byte as char, intermediates);
            }
        }
    }
    
    /// Handle hook sequences (mostly unimplemented)
    fn hook(&mut self, _params: &vte::Params, _intermediates: &[u8], _ignore: bool, _c: char) {
        // TODO: Implement hook handling if needed
    }

    /// Handle put sequences (mostly unimplemented)
    fn put(&mut self, _byte: u8) {
        // TODO: Implement put handling if needed
    }

    /// Handle unhook sequences (mostly unimplemented)
    fn unhook(&mut self) {
        // TODO: Implement unhook handling if needed
    }

    /// Handle OSC sequences (Operating System Commands)
    fn osc_dispatch(&mut self, params: &[&[u8]], _bell_terminated: bool) {
        if params.is_empty() {
            return;
        }

        // Parse OSC command
        let command_str = String::from_utf8_lossy(params[0]);
        let parts: Vec<&str> = command_str.splitn(2, ';').collect();

        if parts.len() < 2 {
            return;
        }

        let command_num = parts[0];
        let data = parts[1];

        match command_num {
            "0" | "2" => {
                // Set window title
                self.handle_set_window_title(data);
            }
            "1" => {
                // Set icon name
                self.handle_set_icon_name(data);
            }
            "4" => {
                // Set color palette
                self.handle_set_color_palette(data);
            }
            "7" => {
                // Set current working directory
                self.handle_set_working_directory(data);
            }
            "8" => {
                // Set hyperlink
                self.handle_set_hyperlink(data);
            }
            "9" => {
                // Desktop notification
                self.handle_desktop_notification(data);
            }
            "10" | "11" | "12" => {
                // Set foreground/background/cursor color
                self.handle_set_dynamic_color(command_num, data);
            }
            "52" => {
                // Clipboard operations
                self.handle_clipboard_operation(data);
            }
            "133" => {
                // Shell integration (Warp-style)
                self.handle_shell_integration(data);
            }
            _ => {
                tracing::debug!("Unhandled OSC command: {}", command_num);
            }
        }
    }

}

impl<'a> ParserPerformer<'a> {
    /// Handle window title setting
    fn handle_set_window_title(&mut self, title: &str) {
        tracing::debug!("Setting window title: {}", title);
        self.state.window_title = Some(title.to_string());
    }

    /// Handle icon name setting
    fn handle_set_icon_name(&mut self, name: &str) {
        tracing::debug!("Setting icon name: {}", name);
        self.state.icon_name = Some(name.to_string());
    }

    /// Handle color palette setting
    fn handle_set_color_palette(&mut self, data: &str) {
        tracing::debug!("Setting color palette: {}", data);
        // Parse color data and update palette
        // Format: "index;rgb:rr/gg/bb"
        if let Some((index_str, color_str)) = data.split_once(';') {
            if let Ok(index) = index_str.parse::<u8>() {
                if let Some(color) = self.parse_color_spec(color_str) {
                    self.state.color_palette.insert(index, color);
                }
            }
        }
    }

    /// Handle working directory setting
    fn handle_set_working_directory(&mut self, path: &str) {
        tracing::debug!("Setting working directory: {}", path);
        self.state.current_directory = Some(path.to_string());
    }

    /// Handle hyperlink setting
    fn handle_set_hyperlink(&mut self, data: &str) {
        tracing::debug!("Setting hyperlink: {}", data);
        // Format: "params;uri" or just ";" to clear
        if data == ";" {
            self.state.current_hyperlink = None;
        } else if let Some((params, uri)) = data.split_once(';') {
            self.state.current_hyperlink = Some(HyperlinkInfo {
                uri: uri.to_string(),
                params: params.to_string(),
            });
        }
    }

    /// Handle desktop notification
    fn handle_desktop_notification(&mut self, data: &str) {
        tracing::debug!("Desktop notification: {}", data);
        // This would trigger a system notification
        // For now, just log it
    }

    /// Handle dynamic color setting
    fn handle_set_dynamic_color(&mut self, command: &str, data: &str) {
        tracing::debug!("Setting dynamic color {}: {}", command, data);
        // This would set foreground, background, or cursor colors
    }

    /// Handle clipboard operations
    fn handle_clipboard_operation(&mut self, data: &str) {
        tracing::debug!("Clipboard operation: {}", data);
        // Format: "c;base64data" for copy, "p;" for paste request
        if let Some((op, content)) = data.split_once(';') {
            match op {
                "c" => {
                    // Copy to clipboard
                    use base64::Engine;
                    let engine = base64::engine::general_purpose::STANDARD;
                    if let Ok(decoded) = engine.decode(content) {
                        if let Ok(text) = String::from_utf8(decoded) {
                            self.state.clipboard_content = Some(text);
                        }
                    }
                }
                "p" => {
                    // Paste request - would trigger paste event
                    tracing::debug!("Paste requested");
                }
                _ => {}
            }
        }
    }

    /// Handle shell integration commands
    fn handle_shell_integration(&mut self, data: &str) {
        tracing::debug!("Shell integration: {}", data);
        // This handles Warp-style shell integration
        // Commands like "A" (command start), "B" (command end), etc.
        match data {
            "A" => {
                // Command started
                self.state.command_state = CommandState::Running;
            }
            "B" => {
                // Command ended
                self.state.command_state = CommandState::Finished;
            }
            "C" => {
                // Command failed
                self.state.command_state = CommandState::Failed;
            }
            _ => {}
        }
    }

    /// Parse color specification
    fn parse_color_spec(&self, spec: &str) -> Option<Color> {
        if spec.starts_with("rgb:") {
            let rgb_part = &spec[4..];
            let parts: Vec<&str> = rgb_part.split('/').collect();
            if parts.len() == 3 {
                if let (Ok(r), Ok(g), Ok(b)) = (
                    u8::from_str_radix(parts[0], 16),
                    u8::from_str_radix(parts[1], 16),
                    u8::from_str_radix(parts[2], 16),
                ) {
                    return Some(Color::Rgb(r, g, b));
                }
            }
        }
        None
    }
}
