//! Pseudoterminal (PTY) management
//! 
//! This module handles communication with shell processes through pseudoterminals,
//! providing the bridge between the terminal UI and the underlying shell.

use crate::config::ConfigManager;
use super::{TerminalError, TerminalResult};
use portable_pty::{Command<PERSON><PERSON>er, PtySize, PtySystem, Child, MasterPty};
use std::sync::Arc;

use tokio::sync::Mutex;

/// PTY manager for shell communication
pub struct PtyManager {
    /// Configuration
    config: Arc<ConfigManager>,
    /// PTY system
    pty_system: Box<dyn PtySystem>,
    /// Master PTY
    master: Option<Box<dyn MasterPty + Send>>,
    /// Child process
    child: Option<Box<dyn Child + Send + Sync>>,
    /// PTY size
    size: PtySize,
    /// Read buffer
    read_buffer: Arc<Mutex<Vec<u8>>>,
}

impl PtyManager {
    /// Create a new PTY manager
    pub async fn new(config: &Arc<ConfigManager>) -> TerminalResult<Self> {
        let pty_system = portable_pty::native_pty_system();
        let size = PtySize {
            rows: 24,
            cols: 80,
            pixel_width: 800,
            pixel_height: 600,
        };
        
        Ok(Self {
            config: config.clone(),
            pty_system,
            master: None,
            child: None,
            size,
            read_buffer: Arc::new(Mutex::new(Vec::new())),
        })
    }
    
    /// Initialize the PTY and spawn shell
    pub async fn initialize(&mut self) -> TerminalResult<()> {
        tracing::info!("Initializing PTY manager");
        
        // Create PTY pair
        let pty_pair = self.pty_system
            .openpty(self.size)
            .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
        
        // Get shell command
        let shell_cmd = self.get_shell_command().await?;
        
        // Spawn shell process
        let child = pty_pair.slave
            .spawn_command(shell_cmd)
            .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
        
        self.master = Some(pty_pair.master);
        self.child = Some(child);
        
        // Start reading from PTY in background
        self.start_reading().await?;
        
        tracing::info!("PTY manager initialized successfully");
        Ok(())
    }
    
    /// Get the shell command to execute
    async fn get_shell_command(&self) -> TerminalResult<CommandBuilder> {
        // Try to get shell from config, environment, or use default
        let shell_path = std::env::var("SHELL")
            .or_else(|_| std::env::var("COMSPEC"))
            .unwrap_or_else(|_| {
                if cfg!(windows) {
                    "powershell.exe".to_string()
                } else {
                    "/bin/bash".to_string()
                }
            });
        
        let mut cmd = CommandBuilder::new(&shell_path);
        
        // Add shell-specific arguments
        if shell_path.contains("bash") {
            cmd.arg("--login");
        } else if shell_path.contains("zsh") {
            cmd.arg("--login");
        } else if shell_path.contains("fish") {
            cmd.arg("--login");
        }
        
        // Set environment variables
        cmd.env("TERM", "xterm-256color");
        cmd.env("COLORTERM", "truecolor");
        cmd.env("X_TERMINAL", "1");
        
        // Set working directory
        if let Ok(cwd) = std::env::current_dir() {
            cmd.cwd(cwd);
        }
        
        Ok(cmd)
    }
    
    /// Start reading from PTY in background
    async fn start_reading(&mut self) -> TerminalResult<()> {
        if let Some(master) = &mut self.master {
            let reader = master.try_clone_reader()
                .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?;

            let buffer = self.read_buffer.clone();

            tokio::spawn(async move {
                use std::io::Read;
                let mut temp_buffer = [0u8; 4096];
                let mut reader = reader;

                loop {
                    match reader.read(&mut temp_buffer) {
                        Ok(0) => {
                            tracing::info!("PTY reader reached EOF");
                            break;
                        }
                        Ok(n) => {
                            let mut buffer = buffer.lock().await;
                            buffer.extend_from_slice(&temp_buffer[..n]);
                        }
                        Err(e) => {
                            tracing::error!("Error reading from PTY: {}", e);
                            break;
                        }
                    }

                    // Small delay to prevent busy waiting
                    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                }
            });
        }

        Ok(())
    }
    
    /// Read available data from PTY
    pub async fn read(&mut self) -> TerminalResult<Vec<u8>> {
        let mut buffer = self.read_buffer.lock().await;
        let data = buffer.clone();
        buffer.clear();
        Ok(data)
    }
    
    /// Write data to PTY
    pub async fn write(&mut self, data: &[u8]) -> TerminalResult<()> {
        if let Some(master) = &mut self.master {
            let mut writer = master.take_writer()
                .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?;

            let data = data.to_vec();
            tokio::task::spawn_blocking(move || {
                use std::io::Write;
                writer.write_all(&data)
            }).await
            .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?
            .map_err(TerminalError::Pty)?;
        }

        Ok(())
    }
    
    /// Resize the PTY
    pub async fn resize(&mut self, rows: u16, cols: u16, pixel_width: u16, pixel_height: u16) -> TerminalResult<()> {
        self.size = PtySize {
            rows,
            cols,
            pixel_width,
            pixel_height,
        };
        
        if let Some(master) = &mut self.master {
            master.resize(self.size)
                .map_err(|e| TerminalError::Pty(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
        }
        
        Ok(())
    }
    
    /// Check if child process is still running
    pub async fn is_running(&mut self) -> bool {
        if let Some(child) = &mut self.child {
            match child.try_wait() {
                Ok(Some(_)) => false, // Process has exited
                Ok(None) => true,     // Process is still running
                Err(_) => false,      // Error checking status
            }
        } else {
            false
        }
    }
    
    /// Get the exit status of the child process
    pub async fn exit_status(&mut self) -> Option<portable_pty::ExitStatus> {
        if let Some(child) = &mut self.child {
            child.try_wait().ok().flatten()
        } else {
            None
        }
    }
}

impl Drop for PtyManager {
    fn drop(&mut self) {
        // Clean up child process
        if let Some(mut child) = self.child.take() {
            let _ = child.kill();
        }
    }
}
