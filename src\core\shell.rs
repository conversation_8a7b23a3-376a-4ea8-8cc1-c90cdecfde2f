//! Shell integration for command blocks and hooks
//! 
//! This module implements shell integration using precmd/preexec hooks
//! to enable block-based command tracking and AI features.

use crate::config::ConfigManager;
use super::{TerminalError, TerminalResult};
use std::sync::Arc;
use uuid::Uuid;

/// Shell integration manager
pub struct ShellIntegration {
    /// Configuration
    config: Arc<ConfigManager>,
    /// Current shell type
    shell_type: ShellType,
    /// Integration hooks
    hooks: ShellHooks,
    /// Current command block
    current_block: Option<CommandBlock>,
}

/// Supported shell types
#[derive(Debug, Clone, PartialEq)]
pub enum ShellType {
    Bash,
    Zsh,
    Fish,
    PowerShell,
    Cmd,
    Unknown(String),
}

/// Shell hooks for command tracking
#[derive(Debug, Clone)]
pub struct ShellHooks {
    /// Pre-command hook (before command execution)
    pub precmd: String,
    /// Pre-execution hook (before command runs)
    pub preexec: String,
    /// Post-command hook (after command completes)
    pub postcmd: String,
}

/// Command block information
#[derive(Debug, <PERSON>lone)]
pub struct CommandBlock {
    /// Block ID
    pub id: Uuid,
    /// Command text
    pub command: String,
    /// Start time
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// End time (if completed)
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// Exit code (if completed)
    pub exit_code: Option<i32>,
    /// Working directory
    pub working_dir: String,
    /// Environment variables
    pub env_vars: std::collections::HashMap<String, String>,
}

impl ShellIntegration {
    /// Create a new shell integration
    pub async fn new(config: &Arc<ConfigManager>) -> TerminalResult<Self> {
        let shell_type = Self::detect_shell().await?;
        let hooks = Self::generate_hooks(&shell_type)?;
        
        Ok(Self {
            config: config.clone(),
            shell_type,
            hooks,
            current_block: None,
        })
    }
    
    /// Initialize shell integration
    pub async fn initialize(&mut self) -> TerminalResult<()> {
        tracing::info!("Initializing shell integration for {:?}", self.shell_type);
        
        // Install hooks based on shell type
        self.install_hooks().await?;
        
        tracing::info!("Shell integration initialized successfully");
        Ok(())
    }
    
    /// Detect the current shell type
    async fn detect_shell() -> TerminalResult<ShellType> {
        let shell_path = std::env::var("SHELL")
            .or_else(|_| std::env::var("COMSPEC"))
            .unwrap_or_else(|_| {
                if cfg!(windows) {
                    "powershell.exe".to_string()
                } else {
                    "/bin/bash".to_string()
                }
            });
        
        let shell_type = if shell_path.contains("bash") {
            ShellType::Bash
        } else if shell_path.contains("zsh") {
            ShellType::Zsh
        } else if shell_path.contains("fish") {
            ShellType::Fish
        } else if shell_path.contains("powershell") || shell_path.contains("pwsh") {
            ShellType::PowerShell
        } else if shell_path.contains("cmd") {
            ShellType::Cmd
        } else {
            ShellType::Unknown(shell_path)
        };
        
        tracing::info!("Detected shell type: {:?}", shell_type);
        Ok(shell_type)
    }
    
    /// Generate hooks for the detected shell
    fn generate_hooks(shell_type: &ShellType) -> TerminalResult<ShellHooks> {
        match shell_type {
            ShellType::Bash => Ok(ShellHooks {
                precmd: Self::bash_precmd_hook(),
                preexec: Self::bash_preexec_hook(),
                postcmd: Self::bash_postcmd_hook(),
            }),
            ShellType::Zsh => Ok(ShellHooks {
                precmd: Self::zsh_precmd_hook(),
                preexec: Self::zsh_preexec_hook(),
                postcmd: Self::zsh_postcmd_hook(),
            }),
            ShellType::Fish => Ok(ShellHooks {
                precmd: Self::fish_precmd_hook(),
                preexec: Self::fish_preexec_hook(),
                postcmd: Self::fish_postcmd_hook(),
            }),
            ShellType::PowerShell => Ok(ShellHooks {
                precmd: Self::powershell_precmd_hook(),
                preexec: Self::powershell_preexec_hook(),
                postcmd: Self::powershell_postcmd_hook(),
            }),
            _ => Err(TerminalError::Shell(format!("Unsupported shell type: {:?}", shell_type))),
        }
    }
    
    /// Install hooks in the shell
    async fn install_hooks(&self) -> TerminalResult<()> {
        match self.shell_type {
            ShellType::Bash => self.install_bash_hooks().await,
            ShellType::Zsh => self.install_zsh_hooks().await,
            ShellType::Fish => self.install_fish_hooks().await,
            ShellType::PowerShell => self.install_powershell_hooks().await,
            _ => Err(TerminalError::Shell("Cannot install hooks for this shell type".to_string())),
        }
    }
    
    /// Install Bash hooks
    async fn install_bash_hooks(&self) -> TerminalResult<()> {
        tracing::info!("Installing Bash hooks");

        // Get the path to the bash hook script
        let hook_script = std::env::current_dir()
            .map_err(|e| TerminalError::Shell(format!("Failed to get current directory: {}", e)))?
            .join("assets")
            .join("shell-hooks")
            .join("bash.sh");

        if hook_script.exists() {
            // Source the hook script
            let source_command = format!("source {}", hook_script.display());
            tracing::info!("Sourcing bash hooks: {}", source_command);

            // Send the source command to install hooks
            self.send_command_to_shell(&source_command).await?;

            tracing::info!("Bash hooks installed successfully");
        } else {
            tracing::warn!("Bash hook script not found: {}", hook_script.display());
            return Err(TerminalError::Shell("Bash hook script not found".to_string()));
        }

        Ok(())
    }

    /// Send a command to the shell for execution
    async fn send_command_to_shell(&self, command: &str) -> TerminalResult<()> {
        tracing::debug!("Sending command to shell: {}", command);

        // In a real implementation, this would send the command to the PTY
        // For now, we'll just log it as a placeholder
        // TODO: Integrate with PTY manager to actually send commands

        tracing::info!("Command sent to shell: {}", command);
        Ok(())
    }

    /// Install Zsh hooks
    async fn install_zsh_hooks(&self) -> TerminalResult<()> {
        tracing::info!("Installing Zsh hooks");

        let hook_script = std::env::current_dir()
            .map_err(|e| TerminalError::Shell(format!("Failed to get current directory: {}", e)))?
            .join("assets")
            .join("shell-hooks")
            .join("zsh.sh");

        if hook_script.exists() {
            let source_command = format!("source {}", hook_script.display());
            tracing::info!("Sourcing zsh hooks: {}", source_command);

            // Send the source command to install hooks
            self.send_command_to_shell(&source_command).await?;

            tracing::info!("Zsh hooks installed successfully");
        } else {
            tracing::warn!("Zsh hook script not found: {}", hook_script.display());
            return Err(TerminalError::Shell("Zsh hook script not found".to_string()));
        }

        Ok(())
    }
    
    /// Install Fish hooks
    async fn install_fish_hooks(&self) -> TerminalResult<()> {
        tracing::info!("Installing Fish hooks");

        let hook_script = std::env::current_dir()
            .map_err(|e| TerminalError::Shell(format!("Failed to get current directory: {}", e)))?
            .join("assets")
            .join("shell-hooks")
            .join("fish.fish");

        if hook_script.exists() {
            let source_command = format!("source {}", hook_script.display());
            tracing::info!("Sourcing fish hooks: {}", source_command);

            // Send the source command to install hooks
            self.send_command_to_shell(&source_command).await?;

            tracing::info!("Fish hooks installed successfully");
        } else {
            tracing::warn!("Fish hook script not found: {}", hook_script.display());
            return Err(TerminalError::Shell("Fish hook script not found".to_string()));
        }

        Ok(())
    }
    
    /// Install PowerShell hooks
    async fn install_powershell_hooks(&self) -> TerminalResult<()> {
        tracing::info!("Installing PowerShell hooks");

        let hook_script = std::env::current_dir()
            .map_err(|e| TerminalError::Shell(format!("Failed to get current directory: {}", e)))?
            .join("assets")
            .join("shell-hooks")
            .join("powershell.ps1");

        if hook_script.exists() {
            let source_command = format!(". {}", hook_script.display());
            tracing::info!("Sourcing PowerShell hooks: {}", source_command);

            // Send the source command to install hooks
            self.send_command_to_shell(&source_command).await?;

            tracing::info!("PowerShell hooks installed successfully");
        } else {
            tracing::warn!("PowerShell hook script not found: {}", hook_script.display());
            return Err(TerminalError::Shell("PowerShell hook script not found".to_string()));
        }

        Ok(())
    }
    
    // Hook generation methods
    fn bash_precmd_hook() -> String {
        r#"
__x_terminal_precmd() {
    local exit_code=$?
    printf '\033]1337;XTerminalHook=precmd;ExitCode=%d;WorkingDir=%s\007' "$exit_code" "$PWD"
}
PROMPT_COMMAND="__x_terminal_precmd; $PROMPT_COMMAND"
"#.to_string()
    }
    
    fn bash_preexec_hook() -> String {
        r#"
__x_terminal_preexec() {
    printf '\033]1337;XTerminalHook=preexec;Command=%s;WorkingDir=%s\007' "$1" "$PWD"
}
trap '__x_terminal_preexec "$BASH_COMMAND"' DEBUG
"#.to_string()
    }
    
    fn bash_postcmd_hook() -> String {
        r#"
__x_terminal_postcmd() {
    local exit_code=$?
    printf '\033]1337;XTerminalHook=postcmd;ExitCode=%d;WorkingDir=%s\007' "$exit_code" "$PWD"
}
"#.to_string()
    }
    
    fn zsh_precmd_hook() -> String {
        r#"
__x_terminal_precmd() {
    local exit_code=$?
    printf '\033]1337;XTerminalHook=precmd;ExitCode=%d;WorkingDir=%s\007' "$exit_code" "$PWD"
}
precmd_functions+=(__x_terminal_precmd)
"#.to_string()
    }
    
    fn zsh_preexec_hook() -> String {
        r#"
__x_terminal_preexec() {
    printf '\033]1337;XTerminalHook=preexec;Command=%s;WorkingDir=%s\007' "$1" "$PWD"
}
preexec_functions+=(__x_terminal_preexec)
"#.to_string()
    }
    
    fn zsh_postcmd_hook() -> String {
        r#"
__x_terminal_postcmd() {
    local exit_code=$?
    printf '\033]1337;XTerminalHook=postcmd;ExitCode=%d;WorkingDir=%s\007' "$exit_code" "$PWD"
}
"#.to_string()
    }
    
    fn fish_precmd_hook() -> String {
        r#"
function __x_terminal_precmd --on-event fish_prompt
    set -l exit_code $status
    printf '\033]1337;XTerminalHook=precmd;ExitCode=%d;WorkingDir=%s\007' $exit_code $PWD
end
"#.to_string()
    }
    
    fn fish_preexec_hook() -> String {
        r#"
function __x_terminal_preexec --on-event fish_preexec
    printf '\033]1337;XTerminalHook=preexec;Command=%s;WorkingDir=%s\007' $argv[1] $PWD
end
"#.to_string()
    }
    
    fn fish_postcmd_hook() -> String {
        r#"
function __x_terminal_postcmd --on-event fish_postexec
    set -l exit_code $status
    printf '\033]1337;XTerminalHook=postcmd;ExitCode=%d;WorkingDir=%s\007' $exit_code $PWD
end
"#.to_string()
    }
    
    fn powershell_precmd_hook() -> String {
        r#"
function prompt {
    $exitCode = $LASTEXITCODE
    Write-Host "`e]1337;XTerminalHook=precmd;ExitCode=$exitCode;WorkingDir=$PWD`a" -NoNewline
    # Return the original prompt
    "PS $($executionContext.SessionState.Path.CurrentLocation)$('>' * ($nestedPromptLevel + 1)) "
}
"#.to_string()
    }
    
    fn powershell_preexec_hook() -> String {
        r#"
# PowerShell doesn't have a direct preexec equivalent
# We'll need to use command history or other mechanisms
"#.to_string()
    }
    
    fn powershell_postcmd_hook() -> String {
        r#"
# PowerShell postcmd hook
"#.to_string()
    }
    
    /// Parse hook event from OSC sequence
    pub fn parse_hook_event(osc_data: &str) -> Option<HookEvent> {
        // Parse OSC sequence like: XTerminalHook=preexec;SessionId=abc;Data=Command=ls;WorkingDir=/home
        let mut hook_type = None;
        let mut session_id = None;
        let mut data = std::collections::HashMap::new();

        for part in osc_data.split(';') {
            if let Some((key, value)) = part.split_once('=') {
                match key {
                    "XTerminalHook" => hook_type = Some(value.to_string()),
                    "SessionId" => session_id = Some(value.to_string()),
                    "Data" => {
                        // Parse data part which contains key=value pairs
                        for data_part in value.split(';') {
                            if let Some((data_key, data_value)) = data_part.split_once('=') {
                                data.insert(data_key.to_string(), data_value.to_string());
                            }
                        }
                    }
                    _ => {}
                }
            }
        }

        if let Some(hook_type_str) = hook_type {
            let hook_type = match hook_type_str.as_str() {
                "precmd" => HookType::Precmd,
                "preexec" => HookType::Preexec,
                "postcmd" => HookType::Postcmd,
                _ => return None,
            };

            Some(HookEvent {
                hook_type,
                command: data.get("Command").cloned(),
                exit_code: data.get("ExitCode").and_then(|s| s.parse().ok()),
                working_dir: data.get("WorkingDir").cloned(),
                timestamp: chrono::Utc::now(),
            })
        } else {
            None
        }
    }

    /// Handle a hook event from the shell
    pub async fn handle_hook_event(&mut self, event: HookEvent) -> TerminalResult<()> {
        match event.hook_type {
            HookType::Precmd => {
                if let Some(block) = &mut self.current_block {
                    // Command completed
                    block.end_time = Some(chrono::Utc::now());
                    block.exit_code = event.exit_code;
                    
                    tracing::info!("Command completed: {} (exit code: {:?})", 
                                 block.command, block.exit_code);
                }
                self.current_block = None;
            }
            HookType::Preexec => {
                // New command starting
                let block = CommandBlock {
                    id: Uuid::new_v4(),
                    command: event.command.unwrap_or_default(),
                    start_time: chrono::Utc::now(),
                    end_time: None,
                    exit_code: None,
                    working_dir: event.working_dir.unwrap_or_default(),
                    env_vars: std::collections::HashMap::new(),
                };
                
                tracing::info!("Command starting: {}", block.command);
                self.current_block = Some(block);
            }
            HookType::Postcmd => {
                // Additional post-command processing if needed
            }
        }
        
        Ok(())
    }
}

/// Hook event from shell
#[derive(Debug, Clone)]
pub struct HookEvent {
    pub hook_type: HookType,
    pub command: Option<String>,
    pub exit_code: Option<i32>,
    pub working_dir: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Hook types
#[derive(Debug, Clone, PartialEq)]
pub enum HookType {
    Precmd,
    Preexec,
    Postcmd,
}
