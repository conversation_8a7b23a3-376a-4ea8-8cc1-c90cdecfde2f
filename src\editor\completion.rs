//! Auto-completion engine for the text editor
//! 
//! This module provides intelligent auto-completion for commands, paths, and other context-aware suggestions.

use super::buffer::TextBuffer;
use anyhow::Result;
use std::collections::HashMap;

/// Completion engine for providing intelligent suggestions
pub struct CompletionEngine {
    /// Command completions
    command_completions: HashMap<String, Vec<String>>,
    /// Path completion cache
    path_cache: HashMap<String, Vec<String>>,
    /// Recent commands for history-based completion
    recent_commands: Vec<String>,
    /// Maximum number of completions to return
    max_completions: usize,
}

/// Completion item
#[derive(Debug, <PERSON>lone)]
pub struct CompletionItem {
    /// The text to insert
    pub text: String,
    /// Display label (may be different from text)
    pub label: String,
    /// Completion type
    pub completion_type: CompletionType,
    /// Additional details
    pub detail: Option<String>,
    /// Documentation
    pub documentation: Option<String>,
}

/// Types of completions
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum CompletionType {
    /// Shell command
    Command,
    /// File or directory path
    Path,
    /// Command line flag/option
    Flag,
    /// Environment variable
    Variable,
    /// Command history
    History,
    /// AI suggestion
    AISuggestion,
}

impl CompletionEngine {
    /// Create a new completion engine
    pub async fn new() -> Result<Self> {
        let mut engine = Self {
            command_completions: HashMap::new(),
            path_cache: HashMap::new(),
            recent_commands: Vec::new(),
            max_completions: 20,
        };
        
        // Initialize with common commands
        engine.initialize_command_completions().await?;
        
        Ok(engine)
    }
    
    /// Initialize common command completions
    async fn initialize_command_completions(&mut self) -> Result<()> {
        // Common Unix/Linux commands
        let common_commands = vec![
            "ls", "cd", "pwd", "mkdir", "rmdir", "rm", "cp", "mv", "cat", "less", "more",
            "grep", "find", "which", "whereis", "man", "info", "help", "history",
            "ps", "top", "htop", "kill", "killall", "jobs", "bg", "fg", "nohup",
            "chmod", "chown", "chgrp", "umask", "ln", "touch", "file", "stat",
            "tar", "gzip", "gunzip", "zip", "unzip", "curl", "wget", "ssh", "scp",
            "git", "cargo", "npm", "pip", "docker", "kubectl", "terraform",
        ];
        
        for cmd in common_commands {
            self.command_completions.insert(cmd.to_string(), Vec::new());
        }
        
        // Add common flags for some commands
        self.command_completions.insert("ls".to_string(), vec![
            "-l".to_string(), "-a".to_string(), "-la".to_string(), "-lh".to_string(),
            "--long".to_string(), "--all".to_string(), "--human-readable".to_string(),
        ]);
        
        self.command_completions.insert("git".to_string(), vec![
            "add".to_string(), "commit".to_string(), "push".to_string(), "pull".to_string(),
            "status".to_string(), "log".to_string(), "diff".to_string(), "branch".to_string(),
            "checkout".to_string(), "merge".to_string(), "rebase".to_string(), "clone".to_string(),
        ]);
        
        Ok(())
    }
    
    /// Get completions for the current buffer position
    pub async fn get_completions(&self, buffer: &TextBuffer, position: usize) -> Result<Vec<String>> {
        let text = buffer.text();
        let current_line = self.get_current_line(text, position);
        let word_info = self.get_current_word_info(&current_line, position);
        
        let mut completions = Vec::new();
        
        // Determine completion context
        if word_info.is_first_word {
            // Complete commands
            completions.extend(self.get_command_completions(&word_info.word).await?);
        } else {
            // Complete arguments, paths, flags, etc.
            completions.extend(self.get_argument_completions(&current_line, &word_info.word).await?);
        }
        
        // Add history-based completions
        completions.extend(self.get_history_completions(&word_info.word).await?);
        
        // Limit and sort completions
        completions.sort();
        completions.dedup();
        completions.truncate(self.max_completions);
        
        Ok(completions)
    }
    
    /// Get current line from buffer
    fn get_current_line(&self, text: &str, position: usize) -> String {
        let lines: Vec<&str> = text.lines().collect();
        let mut char_count = 0;
        
        for line in lines {
            if char_count + line.len() >= position {
                return line.to_string();
            }
            char_count += line.len() + 1; // +1 for newline
        }
        
        String::new()
    }
    
    /// Get information about the current word being typed
    fn get_current_word_info(&self, line: &str, position: usize) -> WordInfo {
        let words: Vec<&str> = line.split_whitespace().collect();
        let is_first_word = words.len() <= 1;
        
        // Find the current word
        let mut word_start = 0;
        for (i, word) in words.iter().enumerate() {
            let word_end = word_start + word.len();
            if position >= word_start && position <= word_end {
                return WordInfo {
                    word: word.to_string(),
                    is_first_word: i == 0,
                    position_in_word: position - word_start,
                };
            }
            word_start = word_end + 1; // +1 for space
        }
        
        // If we're at the end, return empty word
        WordInfo {
            word: String::new(),
            is_first_word,
            position_in_word: 0,
        }
    }
    
    /// Get command completions
    async fn get_command_completions(&self, prefix: &str) -> Result<Vec<String>> {
        let mut completions = Vec::new();
        
        for command in self.command_completions.keys() {
            if command.starts_with(prefix) {
                completions.push(command.clone());
            }
        }
        
        Ok(completions)
    }
    
    /// Get argument completions (paths, flags, etc.)
    async fn get_argument_completions(&self, line: &str, prefix: &str) -> Result<Vec<String>> {
        let mut completions = Vec::new();
        
        // Check if it's a flag
        if prefix.starts_with('-') {
            completions.extend(self.get_flag_completions(line, prefix).await?);
        } else {
            // Try path completion
            completions.extend(self.get_path_completions(prefix).await?);
        }
        
        Ok(completions)
    }
    
    /// Get flag completions for the current command
    async fn get_flag_completions(&self, line: &str, prefix: &str) -> Result<Vec<String>> {
        let words: Vec<&str> = line.split_whitespace().collect();
        if let Some(command) = words.first() {
            if let Some(flags) = self.command_completions.get(*command) {
                return Ok(flags.iter()
                    .filter(|flag| flag.starts_with(prefix))
                    .cloned()
                    .collect());
            }
        }
        
        Ok(Vec::new())
    }
    
    /// Get path completions
    async fn get_path_completions(&self, prefix: &str) -> Result<Vec<String>> {
        // This is a simplified implementation
        // In practice, we'd scan the filesystem
        let mut completions = Vec::new();
        
        // Check cache first
        if let Some(cached) = self.path_cache.get(prefix) {
            return Ok(cached.clone());
        }
        
        // For now, return some common paths
        let common_paths = vec![
            "./", "../", "~/", "/usr/", "/etc/", "/var/", "/tmp/",
        ];
        
        for path in common_paths {
            if path.starts_with(prefix) {
                completions.push(path.to_string());
            }
        }
        
        Ok(completions)
    }
    
    /// Get history-based completions
    async fn get_history_completions(&self, prefix: &str) -> Result<Vec<String>> {
        Ok(self.recent_commands.iter()
            .filter(|cmd| cmd.starts_with(prefix))
            .cloned()
            .collect())
    }
    
    /// Add a command to recent history
    pub fn add_to_history(&mut self, command: String) {
        // Remove if already exists
        self.recent_commands.retain(|cmd| cmd != &command);
        
        // Add to front
        self.recent_commands.insert(0, command);
        
        // Limit history size
        if self.recent_commands.len() > 100 {
            self.recent_commands.truncate(100);
        }
    }
    
    /// Clear path cache
    pub fn clear_path_cache(&mut self) {
        self.path_cache.clear();
    }
    
    /// Set maximum number of completions
    pub fn set_max_completions(&mut self, max: usize) {
        self.max_completions = max;
    }
}

/// Information about the current word being typed
#[derive(Debug)]
struct WordInfo {
    word: String,
    is_first_word: bool,
    position_in_word: usize,
}
