//! Cursor implementation for text editor
//! 
//! This module handles cursor positioning and movement within the text buffer.

use super::buffer::TextBuffer;
use super::CursorMovement;

/// Cursor for text editing
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Cursor {
    /// Current position in characters
    position: usize,
    /// Preferred column for vertical movement
    preferred_column: Option<usize>,
}

impl Cursor {
    /// Create a new cursor at position 0
    pub fn new() -> Self {
        Self {
            position: 0,
            preferred_column: None,
        }
    }
    
    /// Get current cursor position
    pub fn position(&self) -> usize {
        self.position
    }
    
    /// Set cursor position
    pub fn set_position(&mut self, position: usize) {
        self.position = position;
        self.preferred_column = None;
    }
    
    /// Move cursor to end of buffer
    pub fn move_to_end(&mut self, buffer: &TextBuffer) {
        self.position = buffer.len();
        self.preferred_column = None;
    }
    
    /// Move cursor to start of buffer
    pub fn move_to_start(&mut self) {
        self.position = 0;
        self.preferred_column = None;
    }
    
    /// Move cursor based on movement type
    pub fn move_cursor(&mut self, buffer: &TextBuffer, movement: CursorMovement) {
        match movement {
            CursorMovement::Left | CursorMovement::SelectLeft => {
                if self.position > 0 {
                    self.position -= 1;
                }
                self.preferred_column = None;
            }
            CursorMovement::Right | CursorMovement::SelectRight => {
                if self.position < buffer.len() {
                    self.position += 1;
                }
                self.preferred_column = None;
            }
            CursorMovement::Up | CursorMovement::SelectUp => {
                self.move_up(buffer);
            }
            CursorMovement::Down | CursorMovement::SelectDown => {
                self.move_down(buffer);
            }
            CursorMovement::WordLeft | CursorMovement::SelectWordLeft => {
                self.move_word_left(buffer);
            }
            CursorMovement::WordRight | CursorMovement::SelectWordRight => {
                self.move_word_right(buffer);
            }
            CursorMovement::LineStart | CursorMovement::SelectLineStart => {
                self.move_line_start(buffer);
            }
            CursorMovement::LineEnd | CursorMovement::SelectLineEnd => {
                self.move_line_end(buffer);
            }
            CursorMovement::DocumentStart => {
                self.move_to_start();
            }
            CursorMovement::DocumentEnd => {
                self.move_to_end(buffer);
            }
            CursorMovement::SelectAll => {
                // This is handled at a higher level
            }
        }
    }
    
    /// Move cursor up one line
    fn move_up(&mut self, buffer: &TextBuffer) {
        let current_line = buffer.line_at_pos(self.position);
        if current_line > 0 {
            let current_column = buffer.column_at_pos(self.position);
            
            // Use preferred column if available, otherwise use current column
            let target_column = self.preferred_column.unwrap_or(current_column);
            self.preferred_column = Some(target_column);
            
            let target_line = current_line - 1;
            self.position = buffer.pos_from_line_col(target_line, target_column);
        }
    }
    
    /// Move cursor down one line
    fn move_down(&mut self, buffer: &TextBuffer) {
        let current_line = buffer.line_at_pos(self.position);
        if current_line < buffer.line_count() - 1 {
            let current_column = buffer.column_at_pos(self.position);
            
            // Use preferred column if available, otherwise use current column
            let target_column = self.preferred_column.unwrap_or(current_column);
            self.preferred_column = Some(target_column);
            
            let target_line = current_line + 1;
            self.position = buffer.pos_from_line_col(target_line, target_column);
        }
    }
    
    /// Move cursor to previous word boundary
    pub fn move_word_left(&mut self, buffer: &TextBuffer) {
        if self.position == 0 {
            return;
        }
        
        let mut pos = self.position - 1;
        
        // Skip whitespace
        while pos > 0 {
            if let Some(ch) = buffer.char_at(pos) {
                if !ch.is_whitespace() {
                    break;
                }
            }
            pos -= 1;
        }
        
        // Skip word characters
        while pos > 0 {
            if let Some(ch) = buffer.char_at(pos - 1) {
                if ch.is_whitespace() || (!ch.is_alphanumeric() && ch != '_') {
                    break;
                }
            }
            pos -= 1;
        }
        
        self.position = pos;
        self.preferred_column = None;
    }
    
    /// Move cursor to next word boundary
    pub fn move_word_right(&mut self, buffer: &TextBuffer) {
        let mut pos = self.position;
        let len = buffer.len();
        
        if pos >= len {
            return;
        }
        
        // Skip current word
        while pos < len {
            if let Some(ch) = buffer.char_at(pos) {
                if ch.is_whitespace() || (!ch.is_alphanumeric() && ch != '_') {
                    break;
                }
            }
            pos += 1;
        }
        
        // Skip whitespace
        while pos < len {
            if let Some(ch) = buffer.char_at(pos) {
                if !ch.is_whitespace() {
                    break;
                }
            }
            pos += 1;
        }
        
        self.position = pos;
        self.preferred_column = None;
    }
    
    /// Move cursor to start of current line
    fn move_line_start(&mut self, buffer: &TextBuffer) {
        let current_line = buffer.line_at_pos(self.position);
        self.position = buffer.pos_from_line_col(current_line, 0);
        self.preferred_column = None;
    }
    
    /// Move cursor to end of current line
    fn move_line_end(&mut self, buffer: &TextBuffer) {
        let current_line = buffer.line_at_pos(self.position);
        let line_content = buffer.line_content(current_line);
        let line_end = line_content.trim_end().len();
        self.position = buffer.pos_from_line_col(current_line, line_end);
        self.preferred_column = None;
    }
    
    /// Get current line number (0-based)
    pub fn line(&self, buffer: &TextBuffer) -> usize {
        buffer.line_at_pos(self.position)
    }
    
    /// Get current column number (0-based)
    pub fn column(&self, buffer: &TextBuffer) -> usize {
        buffer.column_at_pos(self.position)
    }
    
    /// Check if cursor is at start of buffer
    pub fn is_at_start(&self) -> bool {
        self.position == 0
    }
    
    /// Check if cursor is at end of buffer
    pub fn is_at_end(&self, buffer: &TextBuffer) -> bool {
        self.position >= buffer.len()
    }
    
    /// Check if cursor is at start of line
    pub fn is_at_line_start(&self, buffer: &TextBuffer) -> bool {
        buffer.column_at_pos(self.position) == 0
    }
    
    /// Check if cursor is at end of line
    pub fn is_at_line_end(&self, buffer: &TextBuffer) -> bool {
        let current_line = buffer.line_at_pos(self.position);
        let line_content = buffer.line_content(current_line);
        let current_column = buffer.column_at_pos(self.position);
        current_column >= line_content.trim_end().len()
    }



    /// Move to document start
    pub fn move_to_document_start(&mut self) {
        self.position = 0;
        self.preferred_column = None;
    }

    /// Move to document end
    pub fn move_to_document_end(&mut self, buffer: &TextBuffer) {
        self.move_to_end(buffer);
    }

    /// Move page up (placeholder - would need viewport info)
    pub fn move_page_up(&mut self, buffer: &TextBuffer) {
        // Move up 10 lines as a placeholder
        for _ in 0..10 {
            self.move_up(buffer);
        }
    }

    /// Move page down (placeholder - would need viewport info)
    pub fn move_page_down(&mut self, buffer: &TextBuffer) {
        // Move down 10 lines as a placeholder
        for _ in 0..10 {
            self.move_down(buffer);
        }
    }
}
