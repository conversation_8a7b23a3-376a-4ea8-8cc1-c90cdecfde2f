//! Edit history for undo/redo functionality
//! 
//! This module implements undo/redo functionality for the text editor.

use super::buffer::Edit;
use std::collections::VecDeque;

/// Edit history manager
pub struct EditHistory {
    /// History of edits
    history: VecDeque<Edit>,
    /// Current position in history
    current: usize,
    /// Maximum history size
    max_size: usize,
    /// Whether there are unsaved changes
    has_changes: bool,
    /// Position of last save
    save_position: usize,
}

impl EditHistory {
    /// Create a new edit history
    pub fn new() -> Self {
        Self {
            history: VecDeque::new(),
            current: 0,
            max_size: 1000,
            has_changes: false,
            save_position: 0,
        }
    }
    
    /// Add a new edit to the history
    pub fn add_edit(&mut self, edit: Edit) {
        // Remove any edits after current position (when we're in the middle of history)
        while self.history.len() > self.current {
            self.history.pop_back();
        }
        
        // Add the new edit
        self.history.push_back(edit);
        self.current = self.history.len();
        self.has_changes = true;
        
        // Trim history if it's too large
        while self.history.len() > self.max_size {
            self.history.pop_front();
            if self.current > 0 {
                self.current -= 1;
            }
            if self.save_position > 0 {
                self.save_position -= 1;
            }
        }
    }
    
    /// Undo the last edit
    pub fn undo(&mut self) -> Option<Edit> {
        if self.current > 0 {
            self.current -= 1;
            let edit = self.history[self.current].clone();
            self.update_changes_status();
            Some(edit)
        } else {
            None
        }
    }
    
    /// Redo the next edit
    pub fn redo(&mut self) -> Option<Edit> {
        if self.current < self.history.len() {
            let edit = self.history[self.current].clone();
            self.current += 1;
            self.update_changes_status();
            Some(edit)
        } else {
            None
        }
    }
    
    /// Check if undo is available
    pub fn can_undo(&self) -> bool {
        self.current > 0
    }
    
    /// Check if redo is available
    pub fn can_redo(&self) -> bool {
        self.current < self.history.len()
    }
    
    /// Mark the current state as saved
    pub fn mark_saved(&mut self) {
        self.save_position = self.current;
        self.has_changes = false;
    }
    
    /// Check if there are unsaved changes
    pub fn has_changes(&self) -> bool {
        self.has_changes
    }
    
    /// Update the changes status based on current position
    fn update_changes_status(&mut self) {
        self.has_changes = self.current != self.save_position;
    }
    
    /// Clear all history
    pub fn clear(&mut self) {
        self.history.clear();
        self.current = 0;
        self.has_changes = false;
        self.save_position = 0;
    }
    
    /// Get the number of edits in history
    pub fn len(&self) -> usize {
        self.history.len()
    }
    
    /// Check if history is empty
    pub fn is_empty(&self) -> bool {
        self.history.is_empty()
    }
    
    /// Set maximum history size
    pub fn set_max_size(&mut self, max_size: usize) {
        self.max_size = max_size;
        
        // Trim history if necessary
        while self.history.len() > self.max_size {
            self.history.pop_front();
            if self.current > 0 {
                self.current -= 1;
            }
            if self.save_position > 0 {
                self.save_position -= 1;
            }
        }
    }
    
    /// Get current position in history
    pub fn current_position(&self) -> usize {
        self.current
    }
    
    /// Get save position in history
    pub fn save_position(&self) -> usize {
        self.save_position
    }
}
