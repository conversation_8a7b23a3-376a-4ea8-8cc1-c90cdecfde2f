//! Syntax highlighting for the text editor
//! 
//! This module provides syntax highlighting for shell commands and scripts.

use super::buffer::TextBuffer;
use super::{SyntaxToken, TokenType};
use anyhow::Result;
use std::collections::HashMap;

/// Syntax highlighter for shell commands
pub struct SyntaxHighlighter {
    /// Keywords for different shells
    keywords: HashMap<String, Vec<String>>,
    /// Built-in commands
    builtins: HashMap<String, Vec<String>>,
    /// Common flags pattern
    flag_pattern: regex::Regex,
}

impl SyntaxHighlighter {
    /// Create a new syntax highlighter
    pub async fn new() -> Result<Self> {
        let mut highlighter = Self {
            keywords: HashMap::new(),
            builtins: HashMap::new(),
            flag_pattern: regex::Regex::new(r"^-{1,2}[a-zA-Z0-9-]+")?,
        };
        
        highlighter.initialize_keywords().await?;
        
        Ok(highlighter)
    }
    
    /// Initialize keywords and built-ins
    async fn initialize_keywords(&mut self) -> Result<()> {
        // Bash keywords
        let bash_keywords = vec![
            "if", "then", "else", "elif", "fi", "case", "esac", "for", "while", "until",
            "do", "done", "function", "return", "break", "continue", "local", "export",
            "readonly", "declare", "typeset", "unset", "shift", "eval", "exec", "exit",
            "source", ".", "test", "[", "[[", "]]",
        ];
        
        self.keywords.insert("bash".to_string(), 
            bash_keywords.into_iter().map(String::from).collect());
        
        // Common built-in commands
        let bash_builtins = vec![
            "cd", "pwd", "echo", "printf", "read", "set", "unset", "alias", "unalias",
            "history", "fc", "jobs", "bg", "fg", "kill", "wait", "trap", "ulimit",
            "umask", "times", "type", "which", "command", "builtin", "enable", "help",
        ];
        
        self.builtins.insert("bash".to_string(),
            bash_builtins.into_iter().map(String::from).collect());
        
        Ok(())
    }
    
    /// Highlight syntax in the buffer
    pub fn highlight(&self, buffer: &TextBuffer) -> Vec<SyntaxToken> {
        let text = buffer.text();
        let mut tokens = Vec::new();
        
        // Simple tokenization - split by whitespace and analyze each token
        let mut current_pos = 0;
        let mut in_string = false;
        let mut string_char = '"';
        let mut in_comment = false;
        
        for line in text.lines() {
            let line_start = current_pos;
            
            // Check for comments
            if line.trim_start().starts_with('#') {
                tokens.push(SyntaxToken {
                    start: current_pos,
                    end: current_pos + line.len(),
                    token_type: TokenType::Comment,
                });
                current_pos += line.len() + 1; // +1 for newline
                continue;
            }
            
            // Tokenize the line
            let line_tokens = self.tokenize_line(line, line_start);
            tokens.extend(line_tokens);
            
            current_pos += line.len() + 1; // +1 for newline
        }
        
        tokens
    }
    
    /// Tokenize a single line
    fn tokenize_line(&self, line: &str, line_start: usize) -> Vec<SyntaxToken> {
        let mut tokens = Vec::new();
        let mut current_pos = line_start;
        let mut chars = line.char_indices().peekable();
        let mut in_string = false;
        let mut string_char = '"';
        
        while let Some((i, ch)) = chars.next() {
            let abs_pos = line_start + i;
            
            match ch {
                '"' | '\'' if !in_string => {
                    // Start of string
                    in_string = true;
                    string_char = ch;
                    let end_pos = self.find_string_end(line, i, ch).unwrap_or(line.len());
                    tokens.push(SyntaxToken {
                        start: abs_pos,
                        end: line_start + end_pos,
                        token_type: TokenType::String,
                    });
                    
                    // Skip to end of string
                    while let Some((j, _)) = chars.peek() {
                        if *j >= end_pos {
                            break;
                        }
                        chars.next();
                    }
                    in_string = false;
                }
                '$' => {
                    // Variable
                    let end_pos = self.find_variable_end(line, i).unwrap_or(i + 1);
                    tokens.push(SyntaxToken {
                        start: abs_pos,
                        end: line_start + end_pos,
                        token_type: TokenType::Variable,
                    });
                    
                    // Skip to end of variable
                    while let Some((j, _)) = chars.peek() {
                        if *j >= end_pos {
                            break;
                        }
                        chars.next();
                    }
                }
                '#' => {
                    // Comment (rest of line)
                    tokens.push(SyntaxToken {
                        start: abs_pos,
                        end: line_start + line.len(),
                        token_type: TokenType::Comment,
                    });
                    break;
                }
                _ if ch.is_whitespace() => {
                    // Skip whitespace
                    continue;
                }
                _ => {
                    // Regular token (command, argument, etc.)
                    let word_end = self.find_word_end(line, i);
                    let word = &line[i..word_end];
                    let token_type = self.classify_word(word, tokens.is_empty());
                    
                    tokens.push(SyntaxToken {
                        start: abs_pos,
                        end: line_start + word_end,
                        token_type,
                    });
                    
                    // Skip to end of word
                    while let Some((j, _)) = chars.peek() {
                        if *j >= word_end {
                            break;
                        }
                        chars.next();
                    }
                }
            }
        }
        
        tokens
    }
    
    /// Find the end of a string literal
    fn find_string_end(&self, line: &str, start: usize, quote_char: char) -> Option<usize> {
        let mut chars = line[start + 1..].char_indices();
        let mut escaped = false;
        
        while let Some((i, ch)) = chars.next() {
            if escaped {
                escaped = false;
                continue;
            }
            
            match ch {
                '\\' => escaped = true,
                c if c == quote_char => return Some(start + 1 + i + 1),
                _ => {}
            }
        }
        
        Some(line.len())
    }
    
    /// Find the end of a variable reference
    fn find_variable_end(&self, line: &str, start: usize) -> Option<usize> {
        if start + 1 >= line.len() {
            return Some(start + 1);
        }
        
        let chars: Vec<char> = line.chars().collect();
        let mut pos = start + 1;
        
        // Handle ${var} syntax
        if pos < chars.len() && chars[pos] == '{' {
            pos += 1;
            while pos < chars.len() && chars[pos] != '}' {
                pos += 1;
            }
            if pos < chars.len() {
                pos += 1; // Include closing brace
            }
            return Some(pos);
        }
        
        // Handle $var syntax
        while pos < chars.len() {
            let ch = chars[pos];
            if ch.is_alphanumeric() || ch == '_' {
                pos += 1;
            } else {
                break;
            }
        }
        
        Some(pos)
    }
    
    /// Find the end of a word
    fn find_word_end(&self, line: &str, start: usize) -> usize {
        let chars: Vec<char> = line.chars().collect();
        let mut pos = start;
        
        while pos < chars.len() {
            let ch = chars[pos];
            if ch.is_whitespace() || ch == '"' || ch == '\'' || ch == '#' {
                break;
            }
            pos += 1;
        }
        
        pos
    }
    
    /// Classify a word token
    fn classify_word(&self, word: &str, is_first_word: bool) -> TokenType {
        // Check if it's a number
        if word.parse::<f64>().is_ok() {
            return TokenType::Number;
        }
        
        // Check if it's a flag
        if self.flag_pattern.is_match(word) {
            return TokenType::Flag;
        }
        
        // Check if it's a path
        if word.contains('/') || word.starts_with('~') || word.starts_with('.') {
            return TokenType::Path;
        }
        
        // Check if it's a keyword
        if let Some(bash_keywords) = self.keywords.get("bash") {
            if bash_keywords.contains(&word.to_string()) {
                return TokenType::Keyword;
            }
        }
        
        // Check if it's a built-in command
        if let Some(bash_builtins) = self.builtins.get("bash") {
            if bash_builtins.contains(&word.to_string()) {
                return TokenType::Command;
            }
        }
        
        // If it's the first word, it's likely a command
        if is_first_word {
            TokenType::Command
        } else {
            TokenType::Argument
        }
    }
}
