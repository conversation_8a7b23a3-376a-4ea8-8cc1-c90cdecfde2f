//! X Terminal - Main application entry point

use anyhow::Result;
use clap::Parser;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use x_terminal::{XTerminal, VERSION, NAME, DESCRIPTION};

/// Command line arguments
#[derive(Parser, Debug)]
#[command(name = NAME)]
#[command(version = VERSION)]
#[command(about = DESCRIPTION)]
#[command(long_about = None)]
struct Args {
    /// Enable debug logging
    #[arg(short, long)]
    debug: bool,

    /// Configuration file path
    #[arg(short, long)]
    config: Option<String>,

    /// Start in headless mode (no GUI)
    #[arg(long)]
    headless: bool,

    /// Shell to use (bash, zsh, fish, etc.)
    #[arg(short, long)]
    shell: Option<String>,

    /// Working directory
    #[arg(short, long)]
    working_dir: Option<String>,

    /// Enable AI features
    #[arg(long, default_value = "true")]
    ai: bool,

    /// Enable collaboration features
    #[arg(long, default_value = "true")]
    collaboration: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Initialize logging
    init_logging(args.debug)?;

    tracing::info!("Starting {} v{}", NAME, VERSION);
    tracing::info!("Arguments: {:?}", args);

    // Create and run the terminal
    let terminal = XTerminal::new().await?;
    terminal.run().await?;

    Ok(())
}

/// Initialize logging system
fn init_logging(debug: bool) -> Result<()> {
    let log_level = if debug {
        tracing::Level::DEBUG
    } else {
        tracing::Level::INFO
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| {
                    format!("{}={}", env!("CARGO_PKG_NAME").replace('-', "_"), log_level).into()
                })
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}
