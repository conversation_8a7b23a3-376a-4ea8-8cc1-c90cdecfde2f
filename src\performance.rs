//! Performance optimization utilities
//! 
//! This module provides performance monitoring, optimization, and profiling tools.

use anyhow::Result;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;

/// Performance monitor for tracking application performance
pub struct PerformanceMonitor {
    /// Performance metrics
    metrics: Arc<RwLock<PerformanceMetrics>>,
    /// Frame timing history
    frame_times: Arc<RwLock<Vec<Duration>>>,
    /// Memory usage tracking
    memory_tracker: MemoryTracker,
}

/// Performance metrics
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct PerformanceMetrics {
    /// Average frame time
    pub avg_frame_time: Duration,
    /// Frame rate (FPS)
    pub fps: f64,
    /// Memory usage in bytes
    pub memory_usage: u64,
    /// CPU usage percentage
    pub cpu_usage: f64,
    /// Render time
    pub render_time: Duration,
    /// Input latency
    pub input_latency: Duration,
    /// Number of rendered frames
    pub frame_count: u64,
}

/// Memory usage tracker
#[derive(Debug)]
pub struct MemoryTracker {
    /// Peak memory usage
    peak_usage: u64,
    /// Current allocations
    current_allocations: HashMap<String, u64>,
}

/// Performance timer for measuring operation duration
pub struct PerformanceTimer {
    start_time: Instant,
    operation_name: String,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            frame_times: Arc::new(RwLock::new(Vec::with_capacity(120))), // Store last 2 seconds at 60fps
            memory_tracker: MemoryTracker::new(),
        }
    }
    
    /// Start a performance timer
    pub fn start_timer(&self, operation_name: &str) -> PerformanceTimer {
        PerformanceTimer {
            start_time: Instant::now(),
            operation_name: operation_name.to_string(),
        }
    }
    
    /// Record frame time
    pub async fn record_frame_time(&self, frame_time: Duration) {
        let mut frame_times = self.frame_times.write().await;
        
        // Keep only last 120 frames (2 seconds at 60fps)
        if frame_times.len() >= 120 {
            frame_times.remove(0);
        }
        frame_times.push(frame_time);
        
        // Update metrics
        let mut metrics = self.metrics.write().await;
        metrics.frame_count += 1;
        
        // Calculate average frame time
        let total_time: Duration = frame_times.iter().sum();
        metrics.avg_frame_time = total_time / frame_times.len() as u32;
        
        // Calculate FPS
        metrics.fps = 1.0 / metrics.avg_frame_time.as_secs_f64();
    }
    
    /// Record render time
    pub async fn record_render_time(&self, render_time: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.render_time = render_time;
    }
    
    /// Record input latency
    pub async fn record_input_latency(&self, latency: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.input_latency = latency;
    }
    
    /// Update memory usage
    pub async fn update_memory_usage(&mut self, usage: u64) {
        let mut metrics = self.metrics.write().await;
        metrics.memory_usage = usage;
        
        // Update peak usage
        if usage > self.memory_tracker.peak_usage {
            self.memory_tracker.peak_usage = usage;
        }
    }
    
    /// Get current performance metrics
    pub async fn get_metrics(&self) -> PerformanceMetrics {
        let metrics = self.metrics.read().await;
        metrics.clone()
    }
    
    /// Check if performance is acceptable
    pub async fn is_performance_acceptable(&self) -> bool {
        let metrics = self.metrics.read().await;
        
        // Performance thresholds
        let min_fps = 30.0;
        let max_frame_time = Duration::from_millis(33); // ~30fps
        let max_input_latency = Duration::from_millis(16); // 16ms
        
        metrics.fps >= min_fps &&
        metrics.avg_frame_time <= max_frame_time &&
        metrics.input_latency <= max_input_latency
    }
    
    /// Get performance report
    pub async fn get_performance_report(&self) -> String {
        let metrics = self.metrics.read().await;
        
        format!(
            "Performance Report:\n\
            - FPS: {:.1}\n\
            - Avg Frame Time: {:.2}ms\n\
            - Render Time: {:.2}ms\n\
            - Input Latency: {:.2}ms\n\
            - Memory Usage: {:.2}MB\n\
            - Frame Count: {}\n\
            - Peak Memory: {:.2}MB",
            metrics.fps,
            metrics.avg_frame_time.as_secs_f64() * 1000.0,
            metrics.render_time.as_secs_f64() * 1000.0,
            metrics.input_latency.as_secs_f64() * 1000.0,
            metrics.memory_usage as f64 / 1024.0 / 1024.0,
            metrics.frame_count,
            self.memory_tracker.peak_usage as f64 / 1024.0 / 1024.0
        )
    }
    
    /// Optimize performance based on current metrics
    pub async fn optimize_performance(&self) -> Vec<OptimizationSuggestion> {
        let metrics = self.metrics.read().await;
        let mut suggestions = Vec::new();
        
        // Check FPS
        if metrics.fps < 30.0 {
            suggestions.push(OptimizationSuggestion {
                category: OptimizationCategory::Rendering,
                description: "Low FPS detected. Consider reducing visual effects or resolution.".to_string(),
                priority: OptimizationPriority::High,
            });
        }
        
        // Check frame time
        if metrics.avg_frame_time > Duration::from_millis(33) {
            suggestions.push(OptimizationSuggestion {
                category: OptimizationCategory::Performance,
                description: "High frame time detected. Optimize rendering pipeline.".to_string(),
                priority: OptimizationPriority::Medium,
            });
        }
        
        // Check input latency
        if metrics.input_latency > Duration::from_millis(16) {
            suggestions.push(OptimizationSuggestion {
                category: OptimizationCategory::Input,
                description: "High input latency detected. Optimize input processing.".to_string(),
                priority: OptimizationPriority::High,
            });
        }
        
        // Check memory usage
        if metrics.memory_usage > 1024 * 1024 * 1024 { // 1GB
            suggestions.push(OptimizationSuggestion {
                category: OptimizationCategory::Memory,
                description: "High memory usage detected. Consider memory cleanup.".to_string(),
                priority: OptimizationPriority::Medium,
            });
        }
        
        suggestions
    }
}

impl MemoryTracker {
    fn new() -> Self {
        Self {
            peak_usage: 0,
            current_allocations: HashMap::new(),
        }
    }
    
    /// Track memory allocation
    pub fn track_allocation(&mut self, name: String, size: u64) {
        self.current_allocations.insert(name, size);
    }
    
    /// Track memory deallocation
    pub fn track_deallocation(&mut self, name: &str) {
        self.current_allocations.remove(name);
    }
    
    /// Get current total allocation
    pub fn get_total_allocation(&self) -> u64 {
        self.current_allocations.values().sum()
    }
}

impl PerformanceTimer {
    /// Finish timing and return duration
    pub fn finish(self) -> Duration {
        let duration = self.start_time.elapsed();
        tracing::trace!("Operation '{}' took {:?}", self.operation_name, duration);
        duration
    }
    
    /// Finish timing and log if above threshold
    pub fn finish_with_threshold(self, threshold: Duration) -> Duration {
        let duration = self.start_time.elapsed();
        if duration > threshold {
            tracing::warn!("Slow operation '{}' took {:?} (threshold: {:?})", 
                self.operation_name, duration, threshold);
        }
        duration
    }
}

/// Optimization suggestion
#[derive(Debug, Clone)]
pub struct OptimizationSuggestion {
    pub category: OptimizationCategory,
    pub description: String,
    pub priority: OptimizationPriority,
}

/// Optimization category
#[derive(Debug, Clone)]
pub enum OptimizationCategory {
    Rendering,
    Performance,
    Memory,
    Input,
    Network,
}

/// Optimization priority
#[derive(Debug, Clone)]
pub enum OptimizationPriority {
    Low,
    Medium,
    High,
    Critical,
}

/// Performance optimization utilities
pub struct PerformanceOptimizer;

impl PerformanceOptimizer {
    /// Optimize rendering performance
    pub fn optimize_rendering() -> Vec<String> {
        vec![
            "Enable GPU acceleration".to_string(),
            "Use texture atlasing".to_string(),
            "Implement frustum culling".to_string(),
            "Batch draw calls".to_string(),
            "Use level-of-detail (LOD)".to_string(),
        ]
    }
    
    /// Optimize memory usage
    pub fn optimize_memory() -> Vec<String> {
        vec![
            "Implement object pooling".to_string(),
            "Use memory-mapped files".to_string(),
            "Compress textures".to_string(),
            "Implement garbage collection".to_string(),
            "Use weak references".to_string(),
        ]
    }
    
    /// Optimize input processing
    pub fn optimize_input() -> Vec<String> {
        vec![
            "Use input prediction".to_string(),
            "Implement input buffering".to_string(),
            "Reduce input polling frequency".to_string(),
            "Use hardware acceleration".to_string(),
            "Optimize event handling".to_string(),
        ]
    }
}
