//! Theme management system
//! 
//! This module handles themes, colors, and visual customization.

use crate::config::ConfigManager;
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Theme manager
pub struct ThemeManager {
    config: Arc<ConfigManager>,
    current_theme: Arc<RwLock<Option<Theme>>>,
    available_themes: Vec<String>,
}

/// Theme definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    pub name: String,
    pub colors: ThemeColors,
    pub fonts: ThemeFonts,
    pub ui: ThemeUI,
}

/// Theme colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeColors {
    pub background: String,
    pub foreground: String,
    pub cursor: String,
    pub selection: String,
    pub black: String,
    pub red: String,
    pub green: String,
    pub yellow: String,
    pub blue: String,
    pub magenta: String,
    pub cyan: String,
    pub white: String,
    pub bright_black: String,
    pub bright_red: String,
    pub bright_green: String,
    pub bright_yellow: String,
    pub bright_blue: String,
    pub bright_magenta: String,
    pub bright_cyan: String,
    pub bright_white: String,
}

/// Theme fonts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeFonts {
    pub terminal: String,
    pub ui: String,
    pub size: u32,
}

/// Theme UI settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeUI {
    pub border_radius: u32,
    pub padding: u32,
    pub margin: u32,
    pub shadow: bool,
    pub transparency: f32,
}

impl ThemeManager {
    pub async fn new(config: &Arc<ConfigManager>) -> Result<Self> {
        let available_themes = vec![
            "default".to_string(),
            "dark".to_string(),
            "light".to_string(),
            "monokai".to_string(),
            "solarized".to_string(),
        ];

        Ok(Self {
            config: config.clone(),
            current_theme: Arc::new(RwLock::new(None)),
            available_themes,
        })
    }

    /// Get available themes
    pub fn get_available_themes(&self) -> &[String] {
        &self.available_themes
    }

    /// Get current theme
    pub async fn get_current_theme(&self) -> Option<Theme> {
        let current = self.current_theme.read().await;
        current.clone()
    }

    /// Apply a theme
    pub async fn apply_theme(&self, theme_name: &str) -> Result<()> {
        tracing::info!("Applying theme: {}", theme_name);

        // Load the theme
        let theme = self.load_theme(theme_name).await?;

        // Store as current theme
        {
            let mut current = self.current_theme.write().await;
            *current = Some(theme.clone());
        }

        // Apply theme to UI components
        self.apply_theme_to_components(&theme).await?;

        tracing::info!("Theme '{}' applied successfully", theme_name);
        Ok(())
    }

    /// Load a theme by name
    async fn load_theme(&self, theme_name: &str) -> Result<Theme> {
        // Check built-in themes first
        if let Some(theme) = self.get_builtin_theme(theme_name) {
            return Ok(theme);
        }

        // Try to load from file
        let theme_path = format!("themes/{}.toml", theme_name);
        if let Ok(theme_content) = tokio::fs::read_to_string(&theme_path).await {
            let theme: Theme = toml::from_str(&theme_content)?;
            return Ok(theme);
        }

        // Fallback to default theme
        tracing::warn!("Theme '{}' not found, using default", theme_name);
        Ok(self.get_builtin_theme("default").unwrap())
    }

    /// Get built-in theme
    fn get_builtin_theme(&self, name: &str) -> Option<Theme> {
        match name {
            "default" => Some(self.create_default_theme()),
            "dark" => Some(self.create_dark_theme()),
            "light" => Some(self.create_light_theme()),
            "monokai" => Some(self.create_monokai_theme()),
            "solarized" => Some(self.create_solarized_theme()),
            _ => None,
        }
    }

    /// Create default theme
    fn create_default_theme(&self) -> Theme {
        Theme {
            name: "Default".to_string(),
            colors: ThemeColors {
                background: "#1e1e1e".to_string(),
                foreground: "#d4d4d4".to_string(),
                cursor: "#ffffff".to_string(),
                selection: "#264f78".to_string(),
                black: "#000000".to_string(),
                red: "#cd3131".to_string(),
                green: "#0dbc79".to_string(),
                yellow: "#e5e510".to_string(),
                blue: "#2472c8".to_string(),
                magenta: "#bc3fbc".to_string(),
                cyan: "#11a8cd".to_string(),
                white: "#e5e5e5".to_string(),
                bright_black: "#666666".to_string(),
                bright_red: "#f14c4c".to_string(),
                bright_green: "#23d18b".to_string(),
                bright_yellow: "#f5f543".to_string(),
                bright_blue: "#3b8eea".to_string(),
                bright_magenta: "#d670d6".to_string(),
                bright_cyan: "#29b8db".to_string(),
                bright_white: "#e5e5e5".to_string(),
            },
            fonts: ThemeFonts {
                terminal: "Consolas".to_string(),
                ui: "Segoe UI".to_string(),
                size: 14,
            },
            ui: ThemeUI {
                border_radius: 4,
                padding: 8,
                margin: 4,
                shadow: true,
                transparency: 0.95,
            },
        }
    }

    /// Create dark theme
    fn create_dark_theme(&self) -> Theme {
        let mut theme = self.create_default_theme();
        theme.name = "Dark".to_string();
        theme.colors.background = "#0d1117".to_string();
        theme.colors.foreground = "#c9d1d9".to_string();
        theme.ui.transparency = 0.98;
        theme
    }

    /// Create light theme
    fn create_light_theme(&self) -> Theme {
        let mut theme = self.create_default_theme();
        theme.name = "Light".to_string();
        theme.colors.background = "#ffffff".to_string();
        theme.colors.foreground = "#24292f".to_string();
        theme.colors.selection = "#0969da".to_string();
        theme.ui.transparency = 0.95;
        theme
    }

    /// Create Monokai theme
    fn create_monokai_theme(&self) -> Theme {
        let mut theme = self.create_default_theme();
        theme.name = "Monokai".to_string();
        theme.colors.background = "#272822".to_string();
        theme.colors.foreground = "#f8f8f2".to_string();
        theme.colors.red = "#f92672".to_string();
        theme.colors.green = "#a6e22e".to_string();
        theme.colors.yellow = "#f4bf75".to_string();
        theme.colors.blue = "#66d9ef".to_string();
        theme.colors.magenta = "#ae81ff".to_string();
        theme
    }

    /// Create Solarized theme
    fn create_solarized_theme(&self) -> Theme {
        let mut theme = self.create_default_theme();
        theme.name = "Solarized".to_string();
        theme.colors.background = "#002b36".to_string();
        theme.colors.foreground = "#839496".to_string();
        theme.colors.red = "#dc322f".to_string();
        theme.colors.green = "#859900".to_string();
        theme.colors.yellow = "#b58900".to_string();
        theme.colors.blue = "#268bd2".to_string();
        theme.colors.magenta = "#d33682".to_string();
        theme.colors.cyan = "#2aa198".to_string();
        theme
    }

    /// Apply theme to UI components
    async fn apply_theme_to_components(&self, theme: &Theme) -> Result<()> {
        // TODO: Apply theme to actual UI components
        // This would involve updating colors, fonts, etc. in the rendering system
        tracing::debug!("Applied theme '{}' to UI components", theme.name);
        Ok(())
    }

    /// Save theme to file
    pub async fn save_theme(&self, theme: &Theme, filename: &str) -> Result<()> {
        let theme_content = toml::to_string_pretty(theme)?;
        let theme_path = format!("themes/{}.toml", filename);

        // Create themes directory if it doesn't exist
        if let Some(parent) = std::path::Path::new(&theme_path).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        tokio::fs::write(&theme_path, theme_content).await?;
        tracing::info!("Theme '{}' saved to {}", theme.name, theme_path);
        Ok(())
    }

    /// Create custom theme from base
    pub fn create_custom_theme(&self, base_theme: &str, name: String, modifications: ThemeModifications) -> Result<Theme> {
        let mut theme = self.get_builtin_theme(base_theme)
            .ok_or_else(|| anyhow::anyhow!("Base theme '{}' not found", base_theme))?;

        theme.name = name;

        // Apply modifications
        if let Some(bg) = modifications.background {
            theme.colors.background = bg;
        }
        if let Some(fg) = modifications.foreground {
            theme.colors.foreground = fg;
        }
        if let Some(font) = modifications.terminal_font {
            theme.fonts.terminal = font;
        }
        if let Some(size) = modifications.font_size {
            theme.fonts.size = size;
        }

        Ok(theme)
    }
}

/// Theme modifications for custom themes
#[derive(Debug, Default)]
pub struct ThemeModifications {
    pub background: Option<String>,
    pub foreground: Option<String>,
    pub terminal_font: Option<String>,
    pub font_size: Option<u32>,
}
