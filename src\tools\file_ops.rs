//! File operations module
//! 
//! Comprehensive file and directory operations for X Terminal

use super::{ToolInfo, ToolCategory, ToolParameter, ToolParameterType, ToolResult, ToolResultData, PathContent};
use anyhow::Result;
use std::path::{Path, PathBuf};
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

/// File operations handler
pub struct FileOperations {
    /// Current working directory
    current_dir: PathBuf,
}

impl FileOperations {
    /// Create new file operations handler
    pub async fn new() -> Result<Self> {
        let current_dir = std::env::current_dir()?;
        Ok(Self { current_dir })
    }
    
    /// Initialize file operations
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("File operations initialized");
        Ok(())
    }
    
    /// Get available file operation tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        vec![
            ToolInfo {
                name: "create_file".to_string(),
                description: "Creates a new file with the given content".to_string(),
                category: ToolCategory::FileOperations,
                parameters: vec![
                    ToolParameter {
                        name: "path".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "File path to create".to_string(),
                    },
                    ToolParameter {
                        name: "content".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Content to write to the file".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "read_file".to_string(),
                description: "Reads file (all or specific lines)".to_string(),
                category: ToolCategory::FileOperations,
                parameters: vec![
                    ToolParameter {
                        name: "path".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "File path to read".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "write_file".to_string(),
                description: "Overwrites the file with content".to_string(),
                category: ToolCategory::FileOperations,
                parameters: vec![
                    ToolParameter {
                        name: "path".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "File path to write".to_string(),
                    },
                    ToolParameter {
                        name: "content".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Content to write".to_string(),
                    },
                    ToolParameter {
                        name: "overwrite".to_string(),
                        param_type: ToolParameterType::Boolean,
                        required: false,
                        description: "Whether to overwrite existing file".to_string(),
                    },
                ],
            },
            // Add more tool definitions...
        ]
    }
    
    /// Create a new file with content
    pub async fn create_file(&self, path: &str, content: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        // Create parent directories if they don't exist
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // Check if file already exists
        if file_path.exists() {
            return Ok(ToolResult::error(format!("File already exists: {}", path)));
        }
        
        // Write content to file
        fs::write(file_path, content).await?;
        
        Ok(ToolResult::success(format!("File created successfully: {}", path)))
    }
    
    /// Read file content
    pub async fn read_file(&self, path: &str, lines: Option<(usize, usize)>) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        let content = fs::read_to_string(file_path).await?;
        
        let result_content = if let Some((start, end)) = lines {
            let lines: Vec<&str> = content.lines().collect();
            if start > lines.len() || end > lines.len() || start > end {
                return Ok(ToolResult::error("Invalid line range".to_string()));
            }
            lines[start.saturating_sub(1)..end].join("\n")
        } else {
            content
        };
        
        Ok(ToolResult::success_with_data(
            format!("File read successfully: {}", path),
            ToolResultData::FileContent(result_content),
        ))
    }
    
    /// Write content to file
    pub async fn write_file(&self, path: &str, content: &str, overwrite: bool) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if file_path.exists() && !overwrite {
            return Ok(ToolResult::error(format!("File exists and overwrite is false: {}", path)));
        }
        
        // Create parent directories if they don't exist
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::write(file_path, content).await?;
        
        Ok(ToolResult::success(format!("File written successfully: {}", path)))
    }
    
    /// Append content to file
    pub async fn append_to_file(&self, path: &str, content: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        let mut file = fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(file_path)
            .await?;
        
        file.write_all(content.as_bytes()).await?;
        
        Ok(ToolResult::success(format!("Content appended to file: {}", path)))
    }
    
    /// Delete a file
    pub async fn delete_file(&self, path: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        fs::remove_file(file_path).await?;
        
        Ok(ToolResult::success(format!("File deleted successfully: {}", path)))
    }
    
    /// Rename a file
    pub async fn rename_file(&self, path: &str, new_name: &str) -> Result<ToolResult> {
        let old_path = Path::new(path);
        let new_path = if let Some(parent) = old_path.parent() {
            parent.join(new_name)
        } else {
            PathBuf::from(new_name)
        };
        
        if !old_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        fs::rename(old_path, &new_path).await?;
        
        Ok(ToolResult::success(format!("File renamed from {} to {}", path, new_path.display())))
    }
    
    /// Move a file
    pub async fn move_file(&self, src: &str, dest: &str) -> Result<ToolResult> {
        let src_path = Path::new(src);
        let dest_path = Path::new(dest);
        
        if !src_path.exists() {
            return Ok(ToolResult::error(format!("Source file not found: {}", src)));
        }
        
        // Create destination directory if it doesn't exist
        if let Some(parent) = dest_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::rename(src_path, dest_path).await?;
        
        Ok(ToolResult::success(format!("File moved from {} to {}", src, dest)))
    }
    
    /// Copy a file
    pub async fn copy_file(&self, src: &str, dest: &str) -> Result<ToolResult> {
        let src_path = Path::new(src);
        let dest_path = Path::new(dest);
        
        if !src_path.exists() {
            return Ok(ToolResult::error(format!("Source file not found: {}", src)));
        }
        
        // Create destination directory if it doesn't exist
        if let Some(parent) = dest_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::copy(src_path, dest_path).await?;
        
        Ok(ToolResult::success(format!("File copied from {} to {}", src, dest)))
    }
    
    /// Create backup of a file
    pub async fn backup_file(&self, path: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        let backup_path = format!("{}.bak", path);
        fs::copy(file_path, &backup_path).await?;
        
        Ok(ToolResult::success(format!("Backup created: {}", backup_path)))
    }
    
    /// Insert content into file at specific line
    pub async fn insert_into_file(&self, path: &str, content: &str, line_number: usize) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        let existing_content = fs::read_to_string(file_path).await?;
        let mut lines: Vec<&str> = existing_content.lines().collect();
        
        if line_number > lines.len() + 1 {
            return Ok(ToolResult::error("Line number out of range".to_string()));
        }
        
        lines.insert(line_number.saturating_sub(1), content);
        let new_content = lines.join("\n");
        
        fs::write(file_path, new_content).await?;
        
        Ok(ToolResult::success(format!("Content inserted at line {} in file: {}", line_number, path)))
    }
    
    /// Replace text in file
    pub async fn replace_in_file(&self, path: &str, old: &str, new: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        let content = fs::read_to_string(file_path).await?;
        let new_content = content.replace(old, new);
        
        fs::write(file_path, new_content).await?;
        
        Ok(ToolResult::success(format!("Text replaced in file: {}", path)))
    }
    
    /// Edit a block of lines in file
    pub async fn edit_file_block(&self, path: &str, start_line: usize, end_line: usize, new_content: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        let existing_content = fs::read_to_string(file_path).await?;
        let mut lines: Vec<&str> = existing_content.lines().collect();
        
        if start_line > lines.len() || end_line > lines.len() || start_line > end_line {
            return Ok(ToolResult::error("Invalid line range".to_string()));
        }
        
        // Replace the block
        let start_idx = start_line.saturating_sub(1);
        let end_idx = end_line;
        
        lines.splice(start_idx..end_idx, new_content.lines());
        let final_content = lines.join("\n");
        
        fs::write(file_path, final_content).await?;
        
        Ok(ToolResult::success(format!("Block edited in file: {} (lines {}-{})", path, start_line, end_line)))
    }
    
    /// Clear file content
    pub async fn clear_file(&self, path: &str) -> Result<ToolResult> {
        let file_path = Path::new(path);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", path)));
        }
        
        fs::write(file_path, "").await?;
        
        Ok(ToolResult::success(format!("File cleared: {}", path)))
    }
    
    /// Read multiple files
    pub async fn multi_read(&self, paths: &[String]) -> Result<ToolResult> {
        let mut results = Vec::new();
        
        for path in paths {
            match self.read_file(path, None).await {
                Ok(result) => {
                    if let Some(ToolResultData::FileContent(content)) = result.data {
                        results.push(format!("=== {} ===\n{}", path, content));
                    }
                }
                Err(e) => {
                    results.push(format!("=== {} ===\nError: {}", path, e));
                }
            }
        }
        
        Ok(ToolResult::success_with_data(
            format!("Read {} files", paths.len()),
            ToolResultData::String(results.join("\n\n")),
        ))
    }
    
    /// Write multiple files
    pub async fn multi_write(&self, paths_contents: &[PathContent]) -> Result<ToolResult> {
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for pc in paths_contents {
            match self.write_file(&pc.path, &pc.content, true).await {
                Ok(_) => success_count += 1,
                Err(e) => errors.push(format!("{}: {}", pc.path, e)),
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully wrote {} files", success_count)))
        } else {
            Ok(ToolResult::error(format!("Wrote {} files, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Delete multiple files
    pub async fn multi_delete(&self, paths: &[String]) -> Result<ToolResult> {
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for path in paths {
            match self.delete_file(path).await {
                Ok(_) => success_count += 1,
                Err(e) => errors.push(format!("{}: {}", path, e)),
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully deleted {} files", success_count)))
        } else {
            Ok(ToolResult::error(format!("Deleted {} files, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Move multiple files to directory
    pub async fn multi_move(&self, srcs: &[String], dest_dir: &str) -> Result<ToolResult> {
        let dest_path = Path::new(dest_dir);
        
        // Create destination directory if it doesn't exist
        fs::create_dir_all(dest_path).await?;
        
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for src in srcs {
            let src_path = Path::new(src);
            if let Some(filename) = src_path.file_name() {
                let dest_file = dest_path.join(filename);
                match self.move_file(src, &dest_file.to_string_lossy()).await {
                    Ok(_) => success_count += 1,
                    Err(e) => errors.push(format!("{}: {}", src, e)),
                }
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully moved {} files to {}", success_count, dest_dir)))
        } else {
            Ok(ToolResult::error(format!("Moved {} files, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Create backups for multiple files
    pub async fn multi_backup(&self, paths: &[String]) -> Result<ToolResult> {
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for path in paths {
            match self.backup_file(path).await {
                Ok(_) => success_count += 1,
                Err(e) => errors.push(format!("{}: {}", path, e)),
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully created backups for {} files", success_count)))
        } else {
            Ok(ToolResult::error(format!("Created {} backups, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Batch replace text in multiple files
    pub async fn batch_replace(&self, paths: &[String], find: &str, replace: &str) -> Result<ToolResult> {
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for path in paths {
            match self.replace_in_file(path, find, replace).await {
                Ok(_) => success_count += 1,
                Err(e) => errors.push(format!("{}: {}", path, e)),
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully replaced text in {} files", success_count)))
        } else {
            Ok(ToolResult::error(format!("Replaced text in {} files, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Insert same content into multiple files
    pub async fn multi_insert(&self, paths: &[String], content: &str, line_number: usize) -> Result<ToolResult> {
        let mut success_count = 0;
        let mut errors = Vec::new();
        
        for path in paths {
            match self.insert_into_file(path, content, line_number).await {
                Ok(_) => success_count += 1,
                Err(e) => errors.push(format!("{}: {}", path, e)),
            }
        }
        
        if errors.is_empty() {
            Ok(ToolResult::success(format!("Successfully inserted content into {} files", success_count)))
        } else {
            Ok(ToolResult::error(format!("Inserted content into {} files, {} errors: {}", success_count, errors.len(), errors.join(", "))))
        }
    }
    
    /// Check if file exists
    pub async fn file_exists(&self, path: &str) -> Result<ToolResult> {
        let exists = Path::new(path).exists();
        Ok(ToolResult::success_with_data(
            format!("File exists check for: {}", path),
            ToolResultData::String(exists.to_string()),
        ))
    }
    
    /// Compare two files
    pub async fn compare_files(&self, path1: &str, path2: &str) -> Result<ToolResult> {
        let file1_path = Path::new(path1);
        let file2_path = Path::new(path2);
        
        if !file1_path.exists() {
            return Ok(ToolResult::error(format!("First file not found: {}", path1)));
        }
        
        if !file2_path.exists() {
            return Ok(ToolResult::error(format!("Second file not found: {}", path2)));
        }
        
        let content1 = fs::read_to_string(file1_path).await?;
        let content2 = fs::read_to_string(file2_path).await?;
        
        if content1 == content2 {
            Ok(ToolResult::success("Files are identical".to_string()))
        } else {
            // Simple diff - in a real implementation, you'd use a proper diff algorithm
            let lines1: Vec<&str> = content1.lines().collect();
            let lines2: Vec<&str> = content2.lines().collect();
            
            let mut diff_lines = Vec::new();
            let max_lines = lines1.len().max(lines2.len());
            
            for i in 0..max_lines {
                let line1 = lines1.get(i).unwrap_or(&"");
                let line2 = lines2.get(i).unwrap_or(&"");
                
                if line1 != line2 {
                    diff_lines.push(format!("Line {}: '{}' vs '{}'", i + 1, line1, line2));
                }
            }
            
            Ok(ToolResult::success_with_data(
                "Files differ".to_string(),
                ToolResultData::StringArray(diff_lines),
            ))
        }
    }
    
    /// Create directory
    pub async fn create_directory(&self, path: &str) -> Result<ToolResult> {
        fs::create_dir_all(path).await?;
        Ok(ToolResult::success(format!("Directory created: {}", path)))
    }
    
    /// Delete directory
    pub async fn delete_directory(&self, path: &str, recursive: bool) -> Result<ToolResult> {
        let dir_path = Path::new(path);
        
        if !dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory not found: {}", path)));
        }
        
        if recursive {
            fs::remove_dir_all(dir_path).await?;
        } else {
            fs::remove_dir(dir_path).await?;
        }
        
        Ok(ToolResult::success(format!("Directory deleted: {}", path)))
    }
    
    /// Copy directory
    pub async fn copy_directory(&self, src: &str, dest: &str) -> Result<ToolResult> {
        // This is a simplified implementation - in practice, you'd want a more robust recursive copy
        let src_path = Path::new(src);
        let dest_path = Path::new(dest);
        
        if !src_path.exists() {
            return Ok(ToolResult::error(format!("Source directory not found: {}", src)));
        }
        
        fs::create_dir_all(dest_path).await?;
        
        // Copy files recursively (simplified)
        let mut entries = fs::read_dir(src_path).await?;
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            let dest_entry = dest_path.join(entry.file_name());
            
            if entry_path.is_dir() {
                Box::pin(self.copy_directory(&entry_path.to_string_lossy(), &dest_entry.to_string_lossy())).await?;
            } else {
                fs::copy(&entry_path, &dest_entry).await?;
            }
        }
        
        Ok(ToolResult::success(format!("Directory copied from {} to {}", src, dest)))
    }
    
    /// Move directory
    pub async fn move_directory(&self, src: &str, dest: &str) -> Result<ToolResult> {
        let src_path = Path::new(src);
        let dest_path = Path::new(dest);
        
        if !src_path.exists() {
            return Ok(ToolResult::error(format!("Source directory not found: {}", src)));
        }
        
        fs::rename(src_path, dest_path).await?;
        
        Ok(ToolResult::success(format!("Directory moved from {} to {}", src, dest)))
    }
    
    /// List directory contents
    pub async fn list_directory(&self, path: &str) -> Result<ToolResult> {
        let dir_path = Path::new(path);
        
        if !dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory not found: {}", path)));
        }
        
        let mut entries = fs::read_dir(dir_path).await?;
        let mut items = Vec::new();
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            let item_type = if entry_path.is_dir() { "DIR" } else { "FILE" };
            items.push(format!("{}: {}", item_type, entry.file_name().to_string_lossy()));
        }
        
        Ok(ToolResult::success_with_data(
            format!("Directory listing for: {}", path),
            ToolResultData::StringArray(items),
        ))
    }
    
    /// Get directory tree structure
    pub async fn get_directory_tree(&self, path: &str) -> Result<ToolResult> {
        let tree = self.build_tree(Path::new(path), 0).await?;
        Ok(ToolResult::success_with_data(
            format!("Directory tree for: {}", path),
            ToolResultData::String(tree),
        ))
    }
    
    /// Build directory tree recursively
    async fn build_tree(&self, path: &Path, depth: usize) -> Result<String> {
        let mut tree = String::new();
        let indent = "  ".repeat(depth);
        
        if path.is_dir() {
            tree.push_str(&format!("{}📁 {}\n", indent, path.file_name().unwrap_or_default().to_string_lossy()));
            
            let mut entries = fs::read_dir(path).await?;
            while let Some(entry) = entries.next_entry().await? {
                let subtree = Box::pin(self.build_tree(&entry.path(), depth + 1)).await?;
                tree.push_str(&subtree);
            }
        } else {
            tree.push_str(&format!("{}📄 {}\n", indent, path.file_name().unwrap_or_default().to_string_lossy()));
        }
        
        Ok(tree)
    }
    
    /// Empty directory (delete all contents)
    pub async fn empty_directory(&self, path: &str) -> Result<ToolResult> {
        let dir_path = Path::new(path);
        
        if !dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory not found: {}", path)));
        }
        
        let mut entries = fs::read_dir(dir_path).await?;
        let mut deleted_count = 0;
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                fs::remove_dir_all(&entry_path).await?;
            } else {
                fs::remove_file(&entry_path).await?;
            }
            deleted_count += 1;
        }
        
        Ok(ToolResult::success(format!("Emptied directory: {} ({} items deleted)", path, deleted_count)))
    }
}
