//! Advanced tools system for X Terminal
//! 
//! This module provides comprehensive file operations, terminal tools, and advanced utilities
//! that make X Terminal more powerful than Warp 2.0.

use anyhow::Result;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use tokio::fs;
use tokio::process::Command;
use serde::{Deserialize, Serialize};
use regex::Regex;

pub mod file_ops;
pub mod terminal_ops;
pub mod search_ops;
pub mod project_ops;
pub mod package_ops;

/// Main tools manager
pub struct ToolsManager {
    /// File operations handler
    pub file_ops: file_ops::FileOperations,
    /// Terminal operations handler
    pub terminal_ops: terminal_ops::TerminalOperations,
    /// Search operations handler
    pub search_ops: search_ops::SearchOperations,
    /// Project operations handler
    pub project_ops: project_ops::ProjectOperations,
    /// Package operations handler
    pub package_ops: package_ops::PackageOperations,
}

impl ToolsManager {
    /// Create a new tools manager
    pub async fn new() -> Result<Self> {
        Ok(Self {
            file_ops: file_ops::FileOperations::new().await?,
            terminal_ops: terminal_ops::TerminalOperations::new().await?,
            search_ops: search_ops::SearchOperations::new().await?,
            project_ops: project_ops::ProjectOperations::new().await?,
            package_ops: package_ops::PackageOperations::new().await?,
        })
    }
    
    /// Initialize all tools
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Initializing tools manager");
        
        self.file_ops.initialize().await?;
        self.terminal_ops.initialize().await?;
        self.search_ops.initialize().await?;
        self.project_ops.initialize().await?;
        self.package_ops.initialize().await?;
        
        tracing::info!("Tools manager initialized successfully");
        Ok(())
    }
    
    /// Get available tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        let mut tools = Vec::new();
        
        // File operations
        tools.extend(self.file_ops.get_available_tools());
        
        // Terminal operations
        tools.extend(self.terminal_ops.get_available_tools());
        
        // Search operations
        tools.extend(self.search_ops.get_available_tools());
        
        // Project operations
        tools.extend(self.project_ops.get_available_tools());
        
        // Package operations
        tools.extend(self.package_ops.get_available_tools());
        
        tools
    }
    
    /// Execute a tool by name
    pub async fn execute_tool(&mut self, tool_name: &str, params: ToolParams) -> Result<ToolResult> {
        tracing::info!("Executing tool: {} with params: {:?}", tool_name, params);
        
        let result = match tool_name {
            // File operations
            "create_file" => self.file_ops.create_file(&params.get_string("path")?, &params.get_string("content")?).await,
            "read_file" => self.file_ops.read_file(&params.get_string("path")?, params.get_optional_lines()).await,
            "write_file" => self.file_ops.write_file(&params.get_string("path")?, &params.get_string("content")?, params.get_bool("overwrite").unwrap_or(true)).await,
            "append_to_file" => self.file_ops.append_to_file(&params.get_string("path")?, &params.get_string("content")?).await,
            "delete_file" => self.file_ops.delete_file(&params.get_string("path")?).await,
            "rename_file" => self.file_ops.rename_file(&params.get_string("path")?, &params.get_string("new_name")?).await,
            "move_file" => self.file_ops.move_file(&params.get_string("src")?, &params.get_string("dest")?).await,
            "copy_file" => self.file_ops.copy_file(&params.get_string("src")?, &params.get_string("dest")?).await,
            "backup_file" => self.file_ops.backup_file(&params.get_string("path")?).await,
            "insert_into_file" => self.file_ops.insert_into_file(&params.get_string("path")?, &params.get_string("content")?, params.get_usize("line_number")?).await,
            "replace_in_file" => self.file_ops.replace_in_file(&params.get_string("path")?, &params.get_string("old")?, &params.get_string("new")?).await,
            "edit_file_block" => self.file_ops.edit_file_block(&params.get_string("path")?, params.get_usize("start_line")?, params.get_usize("end_line")?, &params.get_string("new_content")?).await,
            "clear_file" => self.file_ops.clear_file(&params.get_string("path")?).await,
            
            // Multi-file operations
            "multi_read" => self.file_ops.multi_read(&params.get_string_array("paths")?).await,
            "multi_write" => self.file_ops.multi_write(&params.get_path_content_array("paths_contents")?).await,
            "multi_delete" => self.file_ops.multi_delete(&params.get_string_array("paths")?).await,
            "multi_move" => self.file_ops.multi_move(&params.get_string_array("srcs")?, &params.get_string("dest_dir")?).await,
            "multi_backup" => self.file_ops.multi_backup(&params.get_string_array("paths")?).await,
            "batch_replace" => self.file_ops.batch_replace(&params.get_string_array("paths")?, &params.get_string("find")?, &params.get_string("replace")?).await,
            "multi_insert" => self.file_ops.multi_insert(&params.get_string_array("paths")?, &params.get_string("content")?, params.get_usize("line_number")?).await,
            
            // File utilities
            "file_exists" => self.file_ops.file_exists(&params.get_string("path")?).await,
            "compare_files" => self.file_ops.compare_files(&params.get_string("path1")?, &params.get_string("path2")?).await,
            
            // Directory operations
            "create_directory" => self.file_ops.create_directory(&params.get_string("path")?).await,
            "delete_directory" => self.file_ops.delete_directory(&params.get_string("path")?, params.get_bool("recursive").unwrap_or(true)).await,
            "copy_directory" => self.file_ops.copy_directory(&params.get_string("src")?, &params.get_string("dest")?).await,
            "move_directory" => self.file_ops.move_directory(&params.get_string("src")?, &params.get_string("dest")?).await,
            "list_directory" => self.file_ops.list_directory(&params.get_string("path")?).await,
            "get_directory_tree" => self.file_ops.get_directory_tree(&params.get_string("path")?).await,
            "empty_directory" => self.file_ops.empty_directory(&params.get_string("path")?).await,
            
            // Search operations
            "search_files_by_pattern" => self.search_ops.search_files_by_pattern(&params.get_string("pattern")?).await,
            "search_text_in_files" => self.search_ops.search_text_in_files(&params.get_string("pattern")?, &params.get_string_array("file_paths")?).await,
            "grep_search" => self.search_ops.grep_search(&params.get_string("pattern")?, &params.get_string("directory")?).await,
            "semantic_file_search" => self.search_ops.semantic_file_search(&params.get_string("query")?).await,
            
            // Terminal operations
            "run_in_terminal" => self.terminal_ops.run_in_terminal(&params.get_string("command")?).await,
            "get_terminal_output" => self.terminal_ops.get_terminal_output().await,
            "get_terminal_last_command" => self.terminal_ops.get_terminal_last_command().await,
            "get_terminal_selection" => self.terminal_ops.get_terminal_selection().await,
            
            // Project operations
            "backup_project_snapshot" => self.project_ops.backup_project_snapshot(&params.get_string("name")?).await,
            "auto_restore_backup" => self.project_ops.auto_restore_backup(&params.get_string("file_path")?).await,
            "contextual_edit_file" => self.project_ops.contextual_edit_file(&params.get_string("intent")?, &params.get_string("file")?).await,
            "create_and_run_task" => self.project_ops.create_and_run_task(&params.get_string("task_name")?, &params.get_string("command")?).await,
            "get_task_output" => self.project_ops.get_task_output(&params.get_string("task_name")?).await,
            
            // Package operations
            "install_python_packages" => self.package_ops.install_python_packages(&params.get_string_array("packages")?).await,
            "install_npm_packages" => self.package_ops.install_npm_packages(&params.get_string_array("packages")?).await,
            "install_rust_packages" => self.package_ops.install_rust_packages(&params.get_string_array("packages")?).await,
            "install_go_packages" => self.package_ops.install_go_packages(&params.get_string_array("packages")?).await,
            
            // Search operations
            "semantic_web_search" => self.search_ops.semantic_web_search(&params.get_string("query")?).await,
            
            _ => return Err(anyhow::anyhow!("Unknown tool: {}", tool_name)),
        };
        
        match result {
            Ok(result) => {
                tracing::info!("Tool '{}' executed successfully", tool_name);
                Ok(result)
            }
            Err(e) => {
                tracing::error!("Tool '{}' failed: {}", tool_name, e);
                Err(e)
            }
        }
    }
}

/// Tool information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolInfo {
    pub name: String,
    pub description: String,
    pub category: ToolCategory,
    pub parameters: Vec<ToolParameter>,
}

/// Tool category
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolCategory {
    FileOperations,
    TerminalOperations,
    SearchOperations,
    ProjectOperations,
    PackageOperations,
}

/// Tool parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolParameter {
    pub name: String,
    pub param_type: ToolParameterType,
    pub required: bool,
    pub description: String,
}

/// Tool parameter type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolParameterType {
    String,
    StringArray,
    Integer,
    Boolean,
    PathContentArray,
}

/// Tool parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolParams {
    pub params: HashMap<String, ToolParamValue>,
}

/// Tool parameter value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolParamValue {
    String(String),
    StringArray(Vec<String>),
    Integer(i64),
    Boolean(bool),
    PathContentArray(Vec<PathContent>),
}

/// Path and content pair
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathContent {
    pub path: String,
    pub content: String,
}

/// Tool execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    pub success: bool,
    pub message: String,
    pub data: Option<ToolResultData>,
}

/// Tool result data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolResultData {
    String(String),
    StringArray(Vec<String>),
    FileContent(String),
    DirectoryListing(Vec<String>),
    SearchResults(Vec<SearchResult>),
    CommandOutput(CommandOutput),
}

/// Search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub file_path: String,
    pub line_number: usize,
    pub content: String,
    pub context: Option<String>,
}

/// Command output
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandOutput {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: i32,
    pub execution_time: u64,
}

impl ToolParams {
    pub fn new() -> Self {
        Self {
            params: HashMap::new(),
        }
    }
    
    pub fn get_string(&self, key: &str) -> Result<String> {
        match self.params.get(key) {
            Some(ToolParamValue::String(s)) => Ok(s.clone()),
            _ => Err(anyhow::anyhow!("Parameter '{}' not found or not a string", key)),
        }
    }
    
    pub fn get_string_array(&self, key: &str) -> Result<Vec<String>> {
        match self.params.get(key) {
            Some(ToolParamValue::StringArray(arr)) => Ok(arr.clone()),
            _ => Err(anyhow::anyhow!("Parameter '{}' not found or not a string array", key)),
        }
    }
    
    pub fn get_usize(&self, key: &str) -> Result<usize> {
        match self.params.get(key) {
            Some(ToolParamValue::Integer(i)) => Ok(*i as usize),
            _ => Err(anyhow::anyhow!("Parameter '{}' not found or not an integer", key)),
        }
    }
    
    pub fn get_bool(&self, key: &str) -> Option<bool> {
        match self.params.get(key) {
            Some(ToolParamValue::Boolean(b)) => Some(*b),
            _ => None,
        }
    }
    
    pub fn get_optional_lines(&self) -> Option<(usize, usize)> {
        // Implementation for optional line ranges
        None
    }
    
    pub fn get_path_content_array(&self, key: &str) -> Result<Vec<PathContent>> {
        match self.params.get(key) {
            Some(ToolParamValue::PathContentArray(arr)) => Ok(arr.clone()),
            _ => Err(anyhow::anyhow!("Parameter '{}' not found or not a path content array", key)),
        }
    }
}

impl ToolResult {
    pub fn success(message: String) -> Self {
        Self {
            success: true,
            message,
            data: None,
        }
    }
    
    pub fn success_with_data(message: String, data: ToolResultData) -> Self {
        Self {
            success: true,
            message,
            data: Some(data),
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            message,
            data: None,
        }
    }
}
