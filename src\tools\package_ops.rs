//! Package operations module
//! 
//! Package management tools for different programming languages

use super::{ToolInfo, ToolCategory, ToolParameter, ToolParameterType, ToolResult, ToolResultData, CommandOutput};
use anyhow::Result;
use std::path::Path;
use tokio::process::Command;
use std::time::Instant;

/// Package operations handler
pub struct PackageOperations {
    /// Current working directory
    current_dir: std::path::PathBuf,
}

impl PackageOperations {
    /// Create new package operations handler
    pub async fn new() -> Result<Self> {
        let current_dir = std::env::current_dir()?;
        Ok(Self { current_dir })
    }
    
    /// Initialize package operations
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Package operations initialized");
        Ok(())
    }
    
    /// Get available package operation tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        vec![
            ToolInfo {
                name: "install_python_packages".to_string(),
                description: "Install Python packages via pip".to_string(),
                category: ToolCategory::PackageOperations,
                parameters: vec![
                    ToolParameter {
                        name: "packages".to_string(),
                        param_type: ToolParameterType::StringArray,
                        required: true,
                        description: "List of Python packages to install".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "install_npm_packages".to_string(),
                description: "Install Node.js packages via npm".to_string(),
                category: ToolCategory::PackageOperations,
                parameters: vec![
                    ToolParameter {
                        name: "packages".to_string(),
                        param_type: ToolParameterType::StringArray,
                        required: true,
                        description: "List of npm packages to install".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "install_rust_packages".to_string(),
                description: "Install Rust crates via cargo".to_string(),
                category: ToolCategory::PackageOperations,
                parameters: vec![
                    ToolParameter {
                        name: "packages".to_string(),
                        param_type: ToolParameterType::StringArray,
                        required: true,
                        description: "List of Rust crates to install".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "install_go_packages".to_string(),
                description: "Install Go packages via go get".to_string(),
                category: ToolCategory::PackageOperations,
                parameters: vec![
                    ToolParameter {
                        name: "packages".to_string(),
                        param_type: ToolParameterType::StringArray,
                        required: true,
                        description: "List of Go packages to install".to_string(),
                    },
                ],
            },
        ]
    }
    
    /// Install Python packages
    pub async fn install_python_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Installing Python packages: {:?}", packages);
        
        // Check if pip is available
        if !self.command_exists("pip").await? {
            return Ok(ToolResult::error("pip is not available. Please install Python and pip first.".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            tracing::info!("Installing Python package: {}", package);
            
            let start_time = Instant::now();
            let output = Command::new("pip")
                .args(["install", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            let execution_time = start_time.elapsed();
            
            let command_output = CommandOutput {
                stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                exit_code: output.status.code().unwrap_or(-1),
                execution_time: execution_time.as_millis() as u64,
            };
            
            if output.status.success() {
                results.push(format!("✓ {} installed successfully", package));
            } else {
                all_successful = false;
                results.push(format!("✗ {} failed to install: {}", package, command_output.stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} Python packages installed successfully", packages.len())
        } else {
            format!("Python package installation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(
            message,
            ToolResultData::StringArray(results),
        ))
    }
    
    /// Install npm packages
    pub async fn install_npm_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Installing npm packages: {:?}", packages);
        
        // Check if npm is available
        if !self.command_exists("npm").await? {
            return Ok(ToolResult::error("npm is not available. Please install Node.js first.".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            tracing::info!("Installing npm package: {}", package);
            
            let start_time = Instant::now();
            let output = Command::new("npm")
                .args(["install", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            let execution_time = start_time.elapsed();
            
            let command_output = CommandOutput {
                stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                exit_code: output.status.code().unwrap_or(-1),
                execution_time: execution_time.as_millis() as u64,
            };
            
            if output.status.success() {
                results.push(format!("✓ {} installed successfully", package));
            } else {
                all_successful = false;
                results.push(format!("✗ {} failed to install: {}", package, command_output.stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} npm packages installed successfully", packages.len())
        } else {
            format!("npm package installation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(
            message,
            ToolResultData::StringArray(results),
        ))
    }
    
    /// Install Rust packages (crates)
    pub async fn install_rust_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Installing Rust packages: {:?}", packages);
        
        // Check if cargo is available
        if !self.command_exists("cargo").await? {
            return Ok(ToolResult::error("cargo is not available. Please install Rust first.".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            tracing::info!("Installing Rust crate: {}", package);
            
            let start_time = Instant::now();
            let output = Command::new("cargo")
                .args(["add", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            let execution_time = start_time.elapsed();
            
            let command_output = CommandOutput {
                stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                exit_code: output.status.code().unwrap_or(-1),
                execution_time: execution_time.as_millis() as u64,
            };
            
            if output.status.success() {
                results.push(format!("✓ {} added successfully", package));
            } else {
                all_successful = false;
                results.push(format!("✗ {} failed to add: {}", package, command_output.stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} Rust crates added successfully", packages.len())
        } else {
            format!("Rust crate installation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(
            message,
            ToolResultData::StringArray(results),
        ))
    }
    
    /// Install Go packages
    pub async fn install_go_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Installing Go packages: {:?}", packages);
        
        // Check if go is available
        if !self.command_exists("go").await? {
            return Ok(ToolResult::error("go is not available. Please install Go first.".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            tracing::info!("Installing Go package: {}", package);
            
            let start_time = Instant::now();
            let output = Command::new("go")
                .args(["get", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            let execution_time = start_time.elapsed();
            
            let command_output = CommandOutput {
                stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                exit_code: output.status.code().unwrap_or(-1),
                execution_time: execution_time.as_millis() as u64,
            };
            
            if output.status.success() {
                results.push(format!("✓ {} installed successfully", package));
            } else {
                all_successful = false;
                results.push(format!("✗ {} failed to install: {}", package, command_output.stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} Go packages installed successfully", packages.len())
        } else {
            format!("Go package installation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(
            message,
            ToolResultData::StringArray(results),
        ))
    }
    
    /// Check if a command exists
    async fn command_exists(&self, command: &str) -> Result<bool> {
        let check_command = if cfg!(target_os = "windows") {
            Command::new("where")
                .arg(command)
                .output()
                .await?
        } else {
            Command::new("which")
                .arg(command)
                .output()
                .await?
        };
        
        Ok(check_command.status.success())
    }
    
    /// Update Python packages
    pub async fn update_python_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Updating Python packages: {:?}", packages);
        
        if !self.command_exists("pip").await? {
            return Ok(ToolResult::error("pip is not available".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            let output = Command::new("pip")
                .args(["install", "--upgrade", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            if output.status.success() {
                results.push(format!("✓ {} updated successfully", package));
            } else {
                all_successful = false;
                let stderr = String::from_utf8_lossy(&output.stderr);
                results.push(format!("✗ {} failed to update: {}", package, stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} Python packages updated successfully", packages.len())
        } else {
            format!("Python package update completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(message, ToolResultData::StringArray(results)))
    }
    
    /// Update npm packages
    pub async fn update_npm_packages(&self, packages: &[String]) -> Result<ToolResult> {
        tracing::info!("Updating npm packages: {:?}", packages);
        
        if !self.command_exists("npm").await? {
            return Ok(ToolResult::error("npm is not available".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            let output = Command::new("npm")
                .args(["update", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            if output.status.success() {
                results.push(format!("✓ {} updated successfully", package));
            } else {
                all_successful = false;
                let stderr = String::from_utf8_lossy(&output.stderr);
                results.push(format!("✗ {} failed to update: {}", package, stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} npm packages updated successfully", packages.len())
        } else {
            format!("npm package update completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(message, ToolResultData::StringArray(results)))
    }
    
    /// List installed Python packages
    pub async fn list_python_packages(&self) -> Result<ToolResult> {
        if !self.command_exists("pip").await? {
            return Ok(ToolResult::error("pip is not available".to_string()));
        }
        
        let output = Command::new("pip")
            .args(["list"])
            .current_dir(&self.current_dir)
            .output()
            .await?;
        
        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let packages: Vec<String> = stdout.lines()
                .skip(2) // Skip header lines
                .map(|line| line.to_string())
                .collect();
            
            Ok(ToolResult::success_with_data(
                format!("Found {} Python packages", packages.len()),
                ToolResultData::StringArray(packages),
            ))
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Ok(ToolResult::error(format!("Failed to list Python packages: {}", stderr)))
        }
    }
    
    /// List installed npm packages
    pub async fn list_npm_packages(&self) -> Result<ToolResult> {
        if !self.command_exists("npm").await? {
            return Ok(ToolResult::error("npm is not available".to_string()));
        }
        
        let output = Command::new("npm")
            .args(["list", "--depth=0"])
            .current_dir(&self.current_dir)
            .output()
            .await?;
        
        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let packages: Vec<String> = stdout.lines()
                .skip(1) // Skip first line
                .filter(|line| line.contains("├──") || line.contains("└──"))
                .map(|line| line.trim().to_string())
                .collect();
            
            Ok(ToolResult::success_with_data(
                format!("Found {} npm packages", packages.len()),
                ToolResultData::StringArray(packages),
            ))
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Ok(ToolResult::error(format!("Failed to list npm packages: {}", stderr)))
        }
    }
    
    /// Uninstall Python packages
    pub async fn uninstall_python_packages(&self, packages: &[String]) -> Result<ToolResult> {
        if !self.command_exists("pip").await? {
            return Ok(ToolResult::error("pip is not available".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            let output = Command::new("pip")
                .args(["uninstall", "-y", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            if output.status.success() {
                results.push(format!("✓ {} uninstalled successfully", package));
            } else {
                all_successful = false;
                let stderr = String::from_utf8_lossy(&output.stderr);
                results.push(format!("✗ {} failed to uninstall: {}", package, stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} Python packages uninstalled successfully", packages.len())
        } else {
            format!("Python package uninstallation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(message, ToolResultData::StringArray(results)))
    }
    
    /// Uninstall npm packages
    pub async fn uninstall_npm_packages(&self, packages: &[String]) -> Result<ToolResult> {
        if !self.command_exists("npm").await? {
            return Ok(ToolResult::error("npm is not available".to_string()));
        }
        
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for package in packages {
            let output = Command::new("npm")
                .args(["uninstall", package])
                .current_dir(&self.current_dir)
                .output()
                .await?;
            
            if output.status.success() {
                results.push(format!("✓ {} uninstalled successfully", package));
            } else {
                all_successful = false;
                let stderr = String::from_utf8_lossy(&output.stderr);
                results.push(format!("✗ {} failed to uninstall: {}", package, stderr));
            }
        }
        
        let message = if all_successful {
            format!("All {} npm packages uninstalled successfully", packages.len())
        } else {
            format!("npm package uninstallation completed with some failures")
        };
        
        Ok(ToolResult::success_with_data(message, ToolResultData::StringArray(results)))
    }
}
