//! Project operations module
//! 
//! Project management, backup, and task execution tools

use super::{ToolInfo, ToolCategory, ToolParameter, ToolParameterType, ToolResult, ToolResultData, CommandOutput};
use anyhow::Result;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use tokio::fs;
use tokio::process::Command;
use tokio::sync::RwLock;
use std::sync::Arc;
use serde::{Deserialize, Serialize};

/// Project operations handler
pub struct ProjectOperations {
    /// Current project directory
    project_dir: PathBuf,
    /// Active tasks
    active_tasks: Arc<RwLock<HashMap<String, ProjectTask>>>,
    /// Project snapshots
    snapshots: Arc<RwLock<Vec<ProjectSnapshot>>>,
}

/// Project task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectTask {
    pub name: String,
    pub command: String,
    pub status: TaskStatus,
    pub output: Option<CommandOutput>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// Task status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskStatus {
    Created,
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// Project snapshot
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSnapshot {
    pub name: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub file_count: usize,
    pub total_size: u64,
    pub backup_path: String,
}

impl ProjectOperations {
    /// Create new project operations handler
    pub async fn new() -> Result<Self> {
        let project_dir = std::env::current_dir()?;
        Ok(Self {
            project_dir,
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
            snapshots: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    /// Initialize project operations
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Project operations initialized");
        
        // Create backups directory if it doesn't exist
        let backup_dir = self.project_dir.join(".warp_backups");
        fs::create_dir_all(&backup_dir).await?;
        
        Ok(())
    }
    
    /// Get available project operation tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        vec![
            ToolInfo {
                name: "backup_project_snapshot".to_string(),
                description: "Creates a full project backup with a name".to_string(),
                category: ToolCategory::ProjectOperations,
                parameters: vec![
                    ToolParameter {
                        name: "name".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Name for the backup snapshot".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "auto_restore_backup".to_string(),
                description: "Restores a file from the most recent backup".to_string(),
                category: ToolCategory::ProjectOperations,
                parameters: vec![
                    ToolParameter {
                        name: "file_path".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "File path to restore".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "contextual_edit_file".to_string(),
                description: "AI-powered file editing based on intent".to_string(),
                category: ToolCategory::ProjectOperations,
                parameters: vec![
                    ToolParameter {
                        name: "intent".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "What you want to achieve".to_string(),
                    },
                    ToolParameter {
                        name: "file".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "File to edit".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "create_and_run_task".to_string(),
                description: "Creates and executes a named task".to_string(),
                category: ToolCategory::ProjectOperations,
                parameters: vec![
                    ToolParameter {
                        name: "task_name".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Name for the task".to_string(),
                    },
                    ToolParameter {
                        name: "command".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Command to execute".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "get_task_output".to_string(),
                description: "Gets output from a named task".to_string(),
                category: ToolCategory::ProjectOperations,
                parameters: vec![
                    ToolParameter {
                        name: "task_name".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Name of the task".to_string(),
                    },
                ],
            },
        ]
    }
    
    /// Create a project backup snapshot
    pub async fn backup_project_snapshot(&self, name: &str) -> Result<ToolResult> {
        tracing::info!("Creating project backup snapshot: {}", name);
        
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let backup_name = format!("{}_{}", name, timestamp);
        let backup_dir = self.project_dir.join(".warp_backups").join(&backup_name);
        
        // Create backup directory
        fs::create_dir_all(&backup_dir).await?;
        
        // Copy project files (excluding common ignore patterns)
        let (file_count, total_size) = self.copy_project_files(&self.project_dir, &backup_dir).await?;
        
        // Create snapshot record
        let snapshot = ProjectSnapshot {
            name: backup_name.clone(),
            created_at: chrono::Utc::now(),
            file_count,
            total_size,
            backup_path: backup_dir.to_string_lossy().to_string(),
        };
        
        // Store snapshot
        {
            let mut snapshots = self.snapshots.write().await;
            snapshots.push(snapshot);
            
            // Keep only last 10 snapshots
            if snapshots.len() > 10 {
                // Remove oldest snapshot directory
                if let Some(oldest) = snapshots.first() {
                    let _ = fs::remove_dir_all(&oldest.backup_path).await;
                }
                snapshots.remove(0);
            }
        }
        
        Ok(ToolResult::success(format!(
            "Project backup '{}' created successfully ({} files, {:.2} MB)",
            backup_name,
            file_count,
            total_size as f64 / 1024.0 / 1024.0
        )))
    }
    
    /// Copy project files recursively, excluding common ignore patterns
    async fn copy_project_files(&self, src_dir: &Path, dest_dir: &Path) -> Result<(usize, u64)> {
        let mut file_count = 0;
        let mut total_size = 0;
        
        let mut entries = fs::read_dir(src_dir).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            let entry_name = entry.file_name();
            let entry_name_str = entry_name.to_string_lossy();
            
            // Skip common ignore patterns
            if self.should_ignore_for_backup(&entry_name_str) {
                continue;
            }
            
            let dest_path = dest_dir.join(&entry_name);
            
            if entry_path.is_dir() {
                fs::create_dir_all(&dest_path).await?;
                let (sub_files, sub_size) = Box::pin(self.copy_project_files(&entry_path, &dest_path)).await?;
                file_count += sub_files;
                total_size += sub_size;
            } else {
                fs::copy(&entry_path, &dest_path).await?;
                file_count += 1;
                if let Ok(metadata) = entry_path.metadata() {
                    total_size += metadata.len();
                }
            }
        }
        
        Ok((file_count, total_size))
    }
    
    /// Check if file/directory should be ignored for backup
    fn should_ignore_for_backup(&self, name: &str) -> bool {
        matches!(name,
            ".git" | ".svn" | ".hg" |
            "node_modules" | "target" | "build" | "dist" |
            "__pycache__" | ".pytest_cache" | ".mypy_cache" |
            ".DS_Store" | "Thumbs.db" | ".warp_backups" |
            "*.tmp" | "*.log" | "*.cache"
        ) || name.starts_with('.')
    }
    
    /// Auto-restore a file from the most recent backup
    pub async fn auto_restore_backup(&self, file_path: &str) -> Result<ToolResult> {
        tracing::info!("Auto-restoring file from backup: {}", file_path);
        
        let snapshots = self.snapshots.read().await;
        
        if snapshots.is_empty() {
            return Ok(ToolResult::error("No backups available".to_string()));
        }
        
        // Find the most recent backup that contains the file
        for snapshot in snapshots.iter().rev() {
            let backup_file_path = Path::new(&snapshot.backup_path).join(file_path);
            
            if backup_file_path.exists() {
                // Restore the file
                let current_file_path = self.project_dir.join(file_path);
                
                // Create parent directories if needed
                if let Some(parent) = current_file_path.parent() {
                    fs::create_dir_all(parent).await?;
                }
                
                fs::copy(&backup_file_path, &current_file_path).await?;
                
                return Ok(ToolResult::success(format!(
                    "File '{}' restored from backup '{}' ({})",
                    file_path,
                    snapshot.name,
                    snapshot.created_at.format("%Y-%m-%d %H:%M:%S")
                )));
            }
        }
        
        Ok(ToolResult::error(format!("File '{}' not found in any backup", file_path)))
    }
    
    /// Contextual file editing based on intent
    pub async fn contextual_edit_file(&self, intent: &str, file: &str) -> Result<ToolResult> {
        tracing::info!("Contextual edit for file '{}' with intent: {}", file, intent);
        
        let file_path = Path::new(file);
        
        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File not found: {}", file)));
        }
        
        // Read current file content
        let current_content = fs::read_to_string(file_path).await?;
        
        // This is a simplified implementation of contextual editing
        // In a real implementation, you would use AI/LLM to understand the intent
        // and generate appropriate code changes
        
        let suggested_changes = self.analyze_intent_and_suggest_changes(intent, &current_content, file).await?;
        
        Ok(ToolResult::success_with_data(
            format!("Contextual analysis completed for '{}' with intent: {}", file, intent),
            ToolResultData::String(suggested_changes),
        ))
    }
    
    /// Analyze intent and suggest changes (simplified implementation)
    async fn analyze_intent_and_suggest_changes(&self, intent: &str, content: &str, file_path: &str) -> Result<String> {
        // This is a placeholder for AI-powered contextual editing
        // In a real implementation, you would:
        // 1. Parse the intent using NLP
        // 2. Analyze the current code structure
        // 3. Generate appropriate code changes
        // 4. Apply the changes or suggest them to the user
        
        let intent_lower = intent.to_lowercase();
        let file_extension = Path::new(file_path)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");
        
        let suggestions = if intent_lower.contains("add function") || intent_lower.contains("create function") {
            match file_extension {
                "rs" => "// Suggested Rust function:\npub fn new_function() -> Result<()> {\n    // TODO: Implement function\n    Ok(())\n}",
                "py" => "# Suggested Python function:\ndef new_function():\n    \"\"\"TODO: Implement function\"\"\"\n    pass",
                "js" | "ts" => "// Suggested JavaScript/TypeScript function:\nfunction newFunction() {\n    // TODO: Implement function\n}",
                _ => "// Function template for this file type not available",
            }
        } else if intent_lower.contains("add error handling") {
            match file_extension {
                "rs" => "// Suggested error handling:\n// Wrap operations in Result<T, Error>\n// Use ? operator for error propagation\n// Add proper error types",
                "py" => "# Suggested error handling:\n# Add try-except blocks\n# Use specific exception types\n# Add logging for errors",
                "js" | "ts" => "// Suggested error handling:\n// Add try-catch blocks\n// Use proper error types\n// Add error logging",
                _ => "// Error handling suggestions for this file type not available",
            }
        } else if intent_lower.contains("add documentation") || intent_lower.contains("add comments") {
            match file_extension {
                "rs" => "// Suggested documentation:\n/// Add doc comments with ///\n/// Describe function parameters and return values\n/// Add examples with ```rust code blocks",
                "py" => "# Suggested documentation:\n# Add docstrings with triple quotes\n# Describe parameters and return values\n# Add type hints",
                "js" | "ts" => "// Suggested documentation:\n// Add JSDoc comments with /**\n// Describe parameters with @param\n// Describe return values with @returns",
                _ => "// Documentation suggestions for this file type not available",
            }
        } else {
            "// General suggestions:\n// 1. Review code structure\n// 2. Add error handling\n// 3. Improve documentation\n// 4. Add unit tests"
        };
        
        Ok(format!(
            "Contextual Analysis for: {}\nIntent: {}\n\nCurrent file has {} lines\n\n{}\n\nTo apply changes, use the file editing tools with the suggested code.",
            file_path,
            intent,
            content.lines().count(),
            suggestions
        ))
    }
    
    /// Create and run a named task
    pub async fn create_and_run_task(&self, task_name: &str, command: &str) -> Result<ToolResult> {
        tracing::info!("Creating and running task '{}': {}", task_name, command);
        
        // Create task
        let task = ProjectTask {
            name: task_name.to_string(),
            command: command.to_string(),
            status: TaskStatus::Created,
            output: None,
            created_at: chrono::Utc::now(),
            started_at: None,
            completed_at: None,
        };
        
        // Store task
        {
            let mut tasks = self.active_tasks.write().await;
            tasks.insert(task_name.to_string(), task);
        }
        
        // Execute task
        self.execute_task(task_name).await
    }
    
    /// Execute a task
    async fn execute_task(&self, task_name: &str) -> Result<ToolResult> {
        // Update task status to running
        {
            let mut tasks = self.active_tasks.write().await;
            if let Some(task) = tasks.get_mut(task_name) {
                task.status = TaskStatus::Running;
                task.started_at = Some(chrono::Utc::now());
            }
        }
        
        // Get task command
        let command = {
            let tasks = self.active_tasks.read().await;
            tasks.get(task_name).map(|t| t.command.clone())
        };
        
        if let Some(cmd) = command {
            // Execute command
            let start_time = std::time::Instant::now();
            
            let output = if cfg!(target_os = "windows") {
                Command::new("powershell")
                    .args(["-Command", &cmd])
                    .current_dir(&self.project_dir)
                    .output()
                    .await?
            } else {
                Command::new("sh")
                    .args(["-c", &cmd])
                    .current_dir(&self.project_dir)
                    .output()
                    .await?
            };
            
            let execution_time = start_time.elapsed();
            
            let command_output = CommandOutput {
                stdout: String::from_utf8_lossy(&output.stdout).to_string(),
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
                exit_code: output.status.code().unwrap_or(-1),
                execution_time: execution_time.as_millis() as u64,
            };
            
            // Update task with results
            {
                let mut tasks = self.active_tasks.write().await;
                if let Some(task) = tasks.get_mut(task_name) {
                    task.output = Some(command_output.clone());
                    task.completed_at = Some(chrono::Utc::now());
                    task.status = if output.status.success() {
                        TaskStatus::Completed
                    } else {
                        TaskStatus::Failed
                    };
                }
            }
            
            let success = output.status.success();
            let message = if success {
                format!("Task '{}' completed successfully in {}ms", task_name, execution_time.as_millis())
            } else {
                format!("Task '{}' failed with exit code {} in {}ms", task_name, command_output.exit_code, execution_time.as_millis())
            };
            
            if success {
                Ok(ToolResult::success_with_data(message, ToolResultData::CommandOutput(command_output)))
            } else {
                Ok(ToolResult::error(format!("{}\nStderr: {}", message, command_output.stderr)))
            }
        } else {
            Ok(ToolResult::error(format!("Task '{}' not found", task_name)))
        }
    }
    
    /// Get task output
    pub async fn get_task_output(&self, task_name: &str) -> Result<ToolResult> {
        let tasks = self.active_tasks.read().await;
        
        if let Some(task) = tasks.get(task_name) {
            if let Some(output) = &task.output {
                Ok(ToolResult::success_with_data(
                    format!("Task '{}' output (Status: {:?})", task_name, task.status),
                    ToolResultData::CommandOutput(output.clone()),
                ))
            } else {
                Ok(ToolResult::error(format!("Task '{}' has no output yet", task_name)))
            }
        } else {
            Ok(ToolResult::error(format!("Task '{}' not found", task_name)))
        }
    }
    
    /// List all tasks
    pub async fn list_tasks(&self) -> Result<ToolResult> {
        let tasks = self.active_tasks.read().await;
        
        let task_list: Vec<String> = tasks.values()
            .map(|task| format!(
                "{}: {} (Status: {:?}, Created: {})",
                task.name,
                task.command,
                task.status,
                task.created_at.format("%Y-%m-%d %H:%M:%S")
            ))
            .collect();
        
        Ok(ToolResult::success_with_data(
            format!("Found {} tasks", task_list.len()),
            ToolResultData::StringArray(task_list),
        ))
    }
    
    /// List all snapshots
    pub async fn list_snapshots(&self) -> Result<ToolResult> {
        let snapshots = self.snapshots.read().await;
        
        let snapshot_list: Vec<String> = snapshots.iter()
            .map(|snapshot| format!(
                "{}: {} files, {:.2} MB (Created: {})",
                snapshot.name,
                snapshot.file_count,
                snapshot.total_size as f64 / 1024.0 / 1024.0,
                snapshot.created_at.format("%Y-%m-%d %H:%M:%S")
            ))
            .collect();
        
        Ok(ToolResult::success_with_data(
            format!("Found {} snapshots", snapshot_list.len()),
            ToolResultData::StringArray(snapshot_list),
        ))
    }
    
    /// Cancel a running task
    pub async fn cancel_task(&self, task_name: &str) -> Result<ToolResult> {
        let mut tasks = self.active_tasks.write().await;
        
        if let Some(task) = tasks.get_mut(task_name) {
            if matches!(task.status, TaskStatus::Running) {
                task.status = TaskStatus::Cancelled;
                task.completed_at = Some(chrono::Utc::now());
                Ok(ToolResult::success(format!("Task '{}' cancelled", task_name)))
            } else {
                Ok(ToolResult::error(format!("Task '{}' is not running", task_name)))
            }
        } else {
            Ok(ToolResult::error(format!("Task '{}' not found", task_name)))
        }
    }
}
