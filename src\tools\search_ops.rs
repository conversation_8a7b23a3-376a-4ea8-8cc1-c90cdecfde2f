//! Search operations module
//! 
//! File search, text search, and semantic search tools

use super::{ToolInfo, ToolCategory, ToolParameter, ToolParameterType, ToolResult, ToolResultData, SearchResult};
use anyhow::Result;
use std::path::{Path, PathBuf};
use tokio::fs;
use regex::Regex;
use glob::glob;

/// Search operations handler
pub struct SearchOperations {
    /// Current search directory
    current_dir: PathBuf,
}

impl SearchOperations {
    /// Create new search operations handler
    pub async fn new() -> Result<Self> {
        let current_dir = std::env::current_dir()?;
        Ok(Self { current_dir })
    }
    
    /// Initialize search operations
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Search operations initialized");
        Ok(())
    }
    
    /// Get available search operation tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        vec![
            ToolInfo {
                name: "search_files_by_pattern".to_string(),
                description: "Finds files via glob (e.g., *.py)".to_string(),
                category: ToolCategory::SearchOperations,
                parameters: vec![
                    ToolParameter {
                        name: "pattern".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Glob pattern to search for".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "search_text_in_files".to_string(),
                description: "Grep-style search".to_string(),
                category: ToolCategory::SearchOperations,
                parameters: vec![
                    ToolParameter {
                        name: "pattern".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Text pattern to search for".to_string(),
                    },
                    ToolParameter {
                        name: "file_paths".to_string(),
                        param_type: ToolParameterType::StringArray,
                        required: true,
                        description: "Files to search in".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "grep_search".to_string(),
                description: "Regex/keyword text search inside files".to_string(),
                category: ToolCategory::SearchOperations,
                parameters: vec![
                    ToolParameter {
                        name: "pattern".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Regex pattern to search for".to_string(),
                    },
                    ToolParameter {
                        name: "directory".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Directory to search in".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "semantic_file_search".to_string(),
                description: "Natural language file search (e.g., 'find file where database is connected')".to_string(),
                category: ToolCategory::SearchOperations,
                parameters: vec![
                    ToolParameter {
                        name: "query".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Natural language search query".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "semantic_web_search".to_string(),
                description: "Perform Google-like search from a natural query".to_string(),
                category: ToolCategory::SearchOperations,
                parameters: vec![
                    ToolParameter {
                        name: "query".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Search query".to_string(),
                    },
                ],
            },
        ]
    }
    
    /// Search files by glob pattern
    pub async fn search_files_by_pattern(&self, pattern: &str) -> Result<ToolResult> {
        tracing::info!("Searching files with pattern: {}", pattern);
        
        let mut matching_files = Vec::new();
        
        // Use glob to find matching files
        match glob(pattern) {
            Ok(paths) => {
                for entry in paths {
                    match entry {
                        Ok(path) => {
                            matching_files.push(path.to_string_lossy().to_string());
                        }
                        Err(e) => {
                            tracing::warn!("Error processing glob entry: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                return Ok(ToolResult::error(format!("Invalid glob pattern: {}", e)));
            }
        }
        
        Ok(ToolResult::success_with_data(
            format!("Found {} files matching pattern '{}'", matching_files.len(), pattern),
            ToolResultData::StringArray(matching_files),
        ))
    }
    
    /// Search text in specific files
    pub async fn search_text_in_files(&self, pattern: &str, file_paths: &[String]) -> Result<ToolResult> {
        tracing::info!("Searching text '{}' in {} files", pattern, file_paths.len());
        
        let regex = match Regex::new(pattern) {
            Ok(r) => r,
            Err(e) => return Ok(ToolResult::error(format!("Invalid regex pattern: {}", e))),
        };
        
        let mut search_results = Vec::new();
        
        for file_path in file_paths {
            let path = Path::new(file_path);
            
            if !path.exists() {
                tracing::warn!("File not found: {}", file_path);
                continue;
            }
            
            match fs::read_to_string(path).await {
                Ok(content) => {
                    for (line_num, line) in content.lines().enumerate() {
                        if regex.is_match(line) {
                            search_results.push(SearchResult {
                                file_path: file_path.clone(),
                                line_number: line_num + 1,
                                content: line.to_string(),
                                context: None,
                            });
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("Error reading file {}: {}", file_path, e);
                }
            }
        }
        
        Ok(ToolResult::success_with_data(
            format!("Found {} matches for pattern '{}'", search_results.len(), pattern),
            ToolResultData::SearchResults(search_results),
        ))
    }
    
    /// Grep search in directory
    pub async fn grep_search(&self, pattern: &str, directory: &str) -> Result<ToolResult> {
        tracing::info!("Grep searching '{}' in directory: {}", pattern, directory);
        
        let regex = match Regex::new(pattern) {
            Ok(r) => r,
            Err(e) => return Ok(ToolResult::error(format!("Invalid regex pattern: {}", e))),
        };
        
        let mut search_results = Vec::new();
        let dir_path = Path::new(directory);
        
        if !dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory not found: {}", directory)));
        }
        
        self.search_directory_recursive(&regex, dir_path, &mut search_results).await?;
        
        Ok(ToolResult::success_with_data(
            format!("Found {} matches for pattern '{}' in directory '{}'", search_results.len(), pattern, directory),
            ToolResultData::SearchResults(search_results),
        ))
    }
    
    /// Recursively search directory
    async fn search_directory_recursive(&self, regex: &Regex, dir_path: &Path, results: &mut Vec<SearchResult>) -> Result<()> {
        let mut entries = fs::read_dir(dir_path).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                // Skip hidden directories and common ignore patterns
                if let Some(dir_name) = entry_path.file_name() {
                    let dir_name_str = dir_name.to_string_lossy();
                    if dir_name_str.starts_with('.') || 
                       dir_name_str == "node_modules" || 
                       dir_name_str == "target" || 
                       dir_name_str == "__pycache__" {
                        continue;
                    }
                }
                
                // Recursively search subdirectory
                Box::pin(self.search_directory_recursive(regex, &entry_path, results)).await?;
            } else {
                // Search in file
                if let Some(extension) = entry_path.extension() {
                    let ext_str = extension.to_string_lossy();
                    
                    // Only search in text files
                    if self.is_text_file(&ext_str) {
                        self.search_in_file(regex, &entry_path, results).await?;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Check if file extension indicates a text file
    fn is_text_file(&self, extension: &str) -> bool {
        matches!(extension.to_lowercase().as_str(),
            "txt" | "md" | "rs" | "py" | "js" | "ts" | "html" | "css" | "json" | 
            "xml" | "yaml" | "yml" | "toml" | "ini" | "cfg" | "conf" | "log" |
            "c" | "cpp" | "h" | "hpp" | "java" | "go" | "php" | "rb" | "sh" |
            "bat" | "ps1" | "sql" | "r" | "scala" | "kt" | "swift" | "dart"
        )
    }
    
    /// Search for pattern in a specific file
    async fn search_in_file(&self, regex: &Regex, file_path: &Path, results: &mut Vec<SearchResult>) -> Result<()> {
        match fs::read_to_string(file_path).await {
            Ok(content) => {
                let lines: Vec<&str> = content.lines().collect();
                
                for (line_num, line) in lines.iter().enumerate() {
                    if regex.is_match(line) {
                        // Get context lines
                        let context_start = line_num.saturating_sub(2);
                        let context_end = (line_num + 3).min(lines.len());
                        let context = lines[context_start..context_end].join("\n");
                        
                        results.push(SearchResult {
                            file_path: file_path.to_string_lossy().to_string(),
                            line_number: line_num + 1,
                            content: line.to_string(),
                            context: Some(context),
                        });
                    }
                }
            }
            Err(e) => {
                tracing::warn!("Error reading file {}: {}", file_path.display(), e);
            }
        }
        
        Ok(())
    }
    
    /// Semantic file search using natural language
    pub async fn semantic_file_search(&self, query: &str) -> Result<ToolResult> {
        tracing::info!("Semantic file search: {}", query);
        
        // This is a simplified semantic search implementation
        // In a real implementation, you would use embeddings and vector similarity
        
        let mut search_results = Vec::new();
        let keywords = self.extract_keywords(query);
        
        // Search for files that might match the semantic query
        self.semantic_search_directory(&keywords, &self.current_dir, &mut search_results).await?;
        
        // Rank results by relevance (simplified scoring)
        search_results.sort_by(|a, b| {
            let score_a = self.calculate_relevance_score(&keywords, &a.file_path, &a.content);
            let score_b = self.calculate_relevance_score(&keywords, &b.file_path, &b.content);
            score_b.partial_cmp(&score_a).unwrap_or(std::cmp::Ordering::Equal)
        });
        
        // Take top 20 results
        search_results.truncate(20);
        
        Ok(ToolResult::success_with_data(
            format!("Found {} semantic matches for query '{}'", search_results.len(), query),
            ToolResultData::SearchResults(search_results),
        ))
    }
    
    /// Extract keywords from natural language query
    fn extract_keywords(&self, query: &str) -> Vec<String> {
        // Simple keyword extraction - in practice, you'd use NLP libraries
        let stop_words = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "find", "where"];
        
        query.to_lowercase()
            .split_whitespace()
            .filter(|word| !stop_words.contains(word) && word.len() > 2)
            .map(|word| word.to_string())
            .collect()
    }
    
    /// Semantic search in directory
    async fn semantic_search_directory(&self, keywords: &[String], dir_path: &Path, results: &mut Vec<SearchResult>) -> Result<()> {
        let mut entries = fs::read_dir(dir_path).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                // Skip hidden directories and common ignore patterns
                if let Some(dir_name) = entry_path.file_name() {
                    let dir_name_str = dir_name.to_string_lossy();
                    if dir_name_str.starts_with('.') || 
                       dir_name_str == "node_modules" || 
                       dir_name_str == "target" || 
                       dir_name_str == "__pycache__" {
                        continue;
                    }
                }
                
                // Recursively search subdirectory
                Box::pin(self.semantic_search_directory(keywords, &entry_path, results)).await?;
            } else {
                // Search in file
                if let Some(extension) = entry_path.extension() {
                    let ext_str = extension.to_string_lossy();
                    
                    // Only search in text files
                    if self.is_text_file(&ext_str) {
                        self.semantic_search_in_file(keywords, &entry_path, results).await?;
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Semantic search in a specific file
    async fn semantic_search_in_file(&self, keywords: &[String], file_path: &Path, results: &mut Vec<SearchResult>) -> Result<()> {
        match fs::read_to_string(file_path).await {
            Ok(content) => {
                let lines: Vec<&str> = content.lines().collect();
                
                for (line_num, line) in lines.iter().enumerate() {
                    let line_lower = line.to_lowercase();
                    let mut keyword_matches = 0;
                    
                    // Count keyword matches in the line
                    for keyword in keywords {
                        if line_lower.contains(keyword) {
                            keyword_matches += 1;
                        }
                    }
                    
                    // If line contains multiple keywords, it's likely relevant
                    if keyword_matches >= 1 {
                        // Get context lines
                        let context_start = line_num.saturating_sub(3);
                        let context_end = (line_num + 4).min(lines.len());
                        let context = lines[context_start..context_end].join("\n");
                        
                        results.push(SearchResult {
                            file_path: file_path.to_string_lossy().to_string(),
                            line_number: line_num + 1,
                            content: line.to_string(),
                            context: Some(context),
                        });
                    }
                }
            }
            Err(e) => {
                tracing::warn!("Error reading file {}: {}", file_path.display(), e);
            }
        }
        
        Ok(())
    }
    
    /// Calculate relevance score for semantic search
    fn calculate_relevance_score(&self, keywords: &[String], file_path: &str, content: &str) -> f32 {
        let mut score = 0.0;
        let content_lower = content.to_lowercase();
        let file_path_lower = file_path.to_lowercase();
        
        for keyword in keywords {
            // Score for keyword in content
            let content_matches = content_lower.matches(keyword).count() as f32;
            score += content_matches * 1.0;
            
            // Bonus score for keyword in file path
            if file_path_lower.contains(keyword) {
                score += 2.0;
            }
        }
        
        // Normalize by content length
        if !content.is_empty() {
            score / (content.len() as f32).sqrt()
        } else {
            score
        }
    }
    
    /// Semantic web search (simplified implementation)
    pub async fn semantic_web_search(&self, query: &str) -> Result<ToolResult> {
        tracing::info!("Semantic web search: {}", query);
        
        // This is a placeholder implementation
        // In a real implementation, you would integrate with search APIs like Google, Bing, etc.
        
        let search_results = vec![
            format!("Web search result 1 for: {}", query),
            format!("Web search result 2 for: {}", query),
            format!("Web search result 3 for: {}", query),
        ];
        
        Ok(ToolResult::success_with_data(
            format!("Web search completed for query: {}", query),
            ToolResultData::StringArray(search_results),
        ))
    }
    
    /// Find files by content similarity
    pub async fn find_similar_files(&self, reference_file: &str, threshold: f32) -> Result<ToolResult> {
        let ref_path = Path::new(reference_file);
        
        if !ref_path.exists() {
            return Ok(ToolResult::error(format!("Reference file not found: {}", reference_file)));
        }
        
        let reference_content = fs::read_to_string(ref_path).await?;
        let reference_keywords = self.extract_keywords(&reference_content);
        
        let mut similar_files = Vec::new();
        self.find_similar_files_recursive(&reference_keywords, &self.current_dir, &mut similar_files, threshold).await?;
        
        // Sort by similarity score
        similar_files.sort_by(|a, b| {
            let score_a = self.calculate_relevance_score(&reference_keywords, &a.file_path, &a.content);
            let score_b = self.calculate_relevance_score(&reference_keywords, &b.file_path, &b.content);
            score_b.partial_cmp(&score_a).unwrap_or(std::cmp::Ordering::Equal)
        });
        
        Ok(ToolResult::success_with_data(
            format!("Found {} similar files to '{}'", similar_files.len(), reference_file),
            ToolResultData::SearchResults(similar_files),
        ))
    }
    
    /// Find similar files recursively
    async fn find_similar_files_recursive(&self, keywords: &[String], dir_path: &Path, results: &mut Vec<SearchResult>, threshold: f32) -> Result<()> {
        let mut entries = fs::read_dir(dir_path).await?;
        
        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                // Skip hidden directories and common ignore patterns
                if let Some(dir_name) = entry_path.file_name() {
                    let dir_name_str = dir_name.to_string_lossy();
                    if dir_name_str.starts_with('.') || 
                       dir_name_str == "node_modules" || 
                       dir_name_str == "target" || 
                       dir_name_str == "__pycache__" {
                        continue;
                    }
                }
                
                // Recursively search subdirectory
                Box::pin(self.find_similar_files_recursive(keywords, &entry_path, results, threshold)).await?;
            } else {
                // Check file similarity
                if let Some(extension) = entry_path.extension() {
                    let ext_str = extension.to_string_lossy();
                    
                    // Only check text files
                    if self.is_text_file(&ext_str) {
                        if let Ok(content) = fs::read_to_string(&entry_path).await {
                            let similarity_score = self.calculate_relevance_score(keywords, &entry_path.to_string_lossy(), &content);
                            
                            if similarity_score >= threshold {
                                results.push(SearchResult {
                                    file_path: entry_path.to_string_lossy().to_string(),
                                    line_number: 1,
                                    content: format!("Similarity score: {:.2}", similarity_score),
                                    context: Some(content.lines().take(5).collect::<Vec<_>>().join("\n")),
                                });
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
}
