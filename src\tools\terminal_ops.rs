//! Terminal operations module
//! 
//! Terminal command execution and interaction tools

use super::{ToolInfo, ToolCategory, ToolParameter, ToolParameterType, ToolResult, ToolResultData, CommandOutput};
use anyhow::Result;
use std::process::Stdio;
use std::time::{Duration, Instant};
use tokio::process::Command;
use tokio::sync::RwLock;
use std::sync::Arc;

/// Terminal operations handler
pub struct TerminalOperations {
    /// Last command executed
    last_command: Arc<RwLock<Option<String>>>,
    /// Last command output
    last_output: Arc<RwLock<Option<CommandOutput>>>,
    /// Current terminal selection
    current_selection: Arc<RwLock<Option<String>>>,
    /// Command history
    command_history: Arc<RwLock<Vec<String>>>,
}

impl TerminalOperations {
    /// Create new terminal operations handler
    pub async fn new() -> Result<Self> {
        Ok(Self {
            last_command: Arc::new(RwLock::new(None)),
            last_output: Arc::new(RwLock::new(None)),
            current_selection: Arc::new(RwLock::new(None)),
            command_history: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    /// Initialize terminal operations
    pub async fn initialize(&mut self) -> Result<()> {
        tracing::info!("Terminal operations initialized");
        Ok(())
    }
    
    /// Get available terminal operation tools
    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        vec![
            ToolInfo {
                name: "run_in_terminal".to_string(),
                description: "Execute any shell command".to_string(),
                category: ToolCategory::TerminalOperations,
                parameters: vec![
                    ToolParameter {
                        name: "command".to_string(),
                        param_type: ToolParameterType::String,
                        required: true,
                        description: "Command to execute".to_string(),
                    },
                ],
            },
            ToolInfo {
                name: "get_terminal_output".to_string(),
                description: "Capture & analyze last command output".to_string(),
                category: ToolCategory::TerminalOperations,
                parameters: vec![],
            },
            ToolInfo {
                name: "get_terminal_last_command".to_string(),
                description: "Recall last executed command".to_string(),
                category: ToolCategory::TerminalOperations,
                parameters: vec![],
            },
            ToolInfo {
                name: "get_terminal_selection".to_string(),
                description: "What user selected in terminal".to_string(),
                category: ToolCategory::TerminalOperations,
                parameters: vec![],
            },
        ]
    }
    
    /// Execute command in terminal
    pub async fn run_in_terminal(&self, command: &str) -> Result<ToolResult> {
        tracing::info!("Executing terminal command: {}", command);
        
        let start_time = Instant::now();
        
        // Store command in history
        {
            let mut history = self.command_history.write().await;
            history.push(command.to_string());
            
            // Keep only last 100 commands
            if history.len() > 100 {
                history.remove(0);
            }
        }
        
        // Update last command
        {
            let mut last_cmd = self.last_command.write().await;
            *last_cmd = Some(command.to_string());
        }
        
        // Execute command based on platform
        let output = if cfg!(target_os = "windows") {
            Command::new("powershell")
                .args(["-Command", command])
                .stdout(Stdio::piped())
                .stderr(Stdio::piped())
                .output()
                .await?
        } else {
            Command::new("sh")
                .args(["-c", command])
                .stdout(Stdio::piped())
                .stderr(Stdio::piped())
                .output()
                .await?
        };
        
        let execution_time = start_time.elapsed();
        
        let command_output = CommandOutput {
            stdout: String::from_utf8_lossy(&output.stdout).to_string(),
            stderr: String::from_utf8_lossy(&output.stderr).to_string(),
            exit_code: output.status.code().unwrap_or(-1),
            execution_time: execution_time.as_millis() as u64,
        };
        
        // Store output
        {
            let mut last_out = self.last_output.write().await;
            *last_out = Some(command_output.clone());
        }
        
        let success = output.status.success();
        let message = if success {
            format!("Command executed successfully in {}ms", execution_time.as_millis())
        } else {
            format!("Command failed with exit code {} in {}ms", command_output.exit_code, execution_time.as_millis())
        };
        
        if success {
            Ok(ToolResult::success_with_data(message, ToolResultData::CommandOutput(command_output)))
        } else {
            Ok(ToolResult::error(format!("{}\nStderr: {}", message, command_output.stderr)))
        }
    }
    
    /// Get last command output
    pub async fn get_terminal_output(&self) -> Result<ToolResult> {
        let last_output = self.last_output.read().await;
        
        if let Some(output) = last_output.as_ref() {
            Ok(ToolResult::success_with_data(
                "Last command output retrieved".to_string(),
                ToolResultData::CommandOutput(output.clone()),
            ))
        } else {
            Ok(ToolResult::error("No previous command output available".to_string()))
        }
    }
    
    /// Get last executed command
    pub async fn get_terminal_last_command(&self) -> Result<ToolResult> {
        let last_command = self.last_command.read().await;
        
        if let Some(command) = last_command.as_ref() {
            Ok(ToolResult::success_with_data(
                "Last command retrieved".to_string(),
                ToolResultData::String(command.clone()),
            ))
        } else {
            Ok(ToolResult::error("No previous command available".to_string()))
        }
    }
    
    /// Get current terminal selection
    pub async fn get_terminal_selection(&self) -> Result<ToolResult> {
        let selection = self.current_selection.read().await;
        
        if let Some(selected_text) = selection.as_ref() {
            Ok(ToolResult::success_with_data(
                "Terminal selection retrieved".to_string(),
                ToolResultData::String(selected_text.clone()),
            ))
        } else {
            Ok(ToolResult::error("No text selected in terminal".to_string()))
        }
    }
    
    /// Set terminal selection (internal method for terminal integration)
    pub async fn set_terminal_selection(&self, selection: Option<String>) {
        let mut current_sel = self.current_selection.write().await;
        *current_sel = selection;
    }
    
    /// Get command history
    pub async fn get_command_history(&self) -> Result<ToolResult> {
        let history = self.command_history.read().await;
        
        Ok(ToolResult::success_with_data(
            format!("Command history ({} commands)", history.len()),
            ToolResultData::StringArray(history.clone()),
        ))
    }
    
    /// Execute multiple commands in sequence
    pub async fn run_command_sequence(&self, commands: &[String]) -> Result<ToolResult> {
        let mut results = Vec::new();
        let mut all_successful = true;
        
        for (i, command) in commands.iter().enumerate() {
            tracing::info!("Executing command {} of {}: {}", i + 1, commands.len(), command);
            
            match self.run_in_terminal(command).await {
                Ok(result) => {
                    if !result.success {
                        all_successful = false;
                    }
                    results.push(format!("Command {}: {} - {}", i + 1, command, result.message));
                }
                Err(e) => {
                    all_successful = false;
                    results.push(format!("Command {}: {} - Error: {}", i + 1, command, e));
                }
            }
        }
        
        let message = if all_successful {
            format!("All {} commands executed successfully", commands.len())
        } else {
            format!("Executed {} commands with some failures", commands.len())
        };
        
        Ok(ToolResult::success_with_data(
            message,
            ToolResultData::StringArray(results),
        ))
    }
    
    /// Execute command with timeout
    pub async fn run_with_timeout(&self, command: &str, timeout_secs: u64) -> Result<ToolResult> {
        let timeout_duration = Duration::from_secs(timeout_secs);
        
        match tokio::time::timeout(timeout_duration, self.run_in_terminal(command)).await {
            Ok(result) => result,
            Err(_) => Ok(ToolResult::error(format!("Command timed out after {} seconds", timeout_secs))),
        }
    }
    
    /// Execute command in background
    pub async fn run_in_background(&self, command: &str) -> Result<ToolResult> {
        tracing::info!("Starting background command: {}", command);
        
        let background_command = if cfg!(target_os = "windows") {
            format!("Start-Process powershell -ArgumentList '-Command', '{}' -WindowStyle Hidden", command)
        } else {
            format!("{} &", command)
        };
        
        self.run_in_terminal(&background_command).await
    }
    
    /// Get system information
    pub async fn get_system_info(&self) -> Result<ToolResult> {
        let commands = if cfg!(target_os = "windows") {
            vec![
                "Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory".to_string(),
                "$env:PROCESSOR_IDENTIFIER".to_string(),
                "Get-Location".to_string(),
            ]
        } else {
            vec![
                "uname -a".to_string(),
                "whoami".to_string(),
                "pwd".to_string(),
                "df -h".to_string(),
            ]
        };
        
        let mut system_info = Vec::new();
        
        for cmd in commands {
            match self.run_in_terminal(&cmd).await {
                Ok(result) => {
                    if let Some(ToolResultData::CommandOutput(output)) = result.data {
                        system_info.push(format!("$ {}\n{}", cmd, output.stdout));
                    }
                }
                Err(e) => {
                    system_info.push(format!("$ {} - Error: {}", cmd, e));
                }
            }
        }
        
        Ok(ToolResult::success_with_data(
            "System information retrieved".to_string(),
            ToolResultData::String(system_info.join("\n\n")),
        ))
    }
    
    /// Check if command exists
    pub async fn command_exists(&self, command: &str) -> Result<ToolResult> {
        let check_command = if cfg!(target_os = "windows") {
            format!("Get-Command {} -ErrorAction SilentlyContinue", command)
        } else {
            format!("which {}", command)
        };
        
        match self.run_in_terminal(&check_command).await {
            Ok(result) => {
                let exists = result.success;
                Ok(ToolResult::success_with_data(
                    format!("Command '{}' exists: {}", command, exists),
                    ToolResultData::String(exists.to_string()),
                ))
            }
            Err(_) => {
                Ok(ToolResult::success_with_data(
                    format!("Command '{}' exists: false", command),
                    ToolResultData::String("false".to_string()),
                ))
            }
        }
    }
    
    /// Get environment variables
    pub async fn get_env_vars(&self) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            "Get-ChildItem Env: | Format-Table Name, Value -AutoSize"
        } else {
            "env | sort"
        };
        
        self.run_in_terminal(command).await
    }
    
    /// Set environment variable
    pub async fn set_env_var(&self, name: &str, value: &str) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            format!("$env:{} = '{}'", name, value)
        } else {
            format!("export {}='{}'", name, value)
        };
        
        self.run_in_terminal(&command).await
    }
    
    /// Get current working directory
    pub async fn get_current_directory(&self) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            "Get-Location"
        } else {
            "pwd"
        };
        
        self.run_in_terminal(command).await
    }
    
    /// Change directory
    pub async fn change_directory(&self, path: &str) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            format!("Set-Location '{}'", path)
        } else {
            format!("cd '{}'", path)
        };
        
        self.run_in_terminal(&command).await
    }
    
    /// List processes
    pub async fn list_processes(&self) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            "Get-Process | Select-Object Name, Id, CPU, WorkingSet | Sort-Object CPU -Descending | Select-Object -First 20"
        } else {
            "ps aux | head -20"
        };
        
        self.run_in_terminal(command).await
    }
    
    /// Kill process by name or ID
    pub async fn kill_process(&self, identifier: &str) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            // Try to parse as PID first, then as process name
            if identifier.parse::<u32>().is_ok() {
                format!("Stop-Process -Id {} -Force", identifier)
            } else {
                format!("Stop-Process -Name '{}' -Force", identifier)
            }
        } else {
            if identifier.parse::<u32>().is_ok() {
                format!("kill -9 {}", identifier)
            } else {
                format!("pkill -f '{}'", identifier)
            }
        };
        
        self.run_in_terminal(&command).await
    }
    
    /// Get network information
    pub async fn get_network_info(&self) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            "Get-NetIPConfiguration | Select-Object InterfaceAlias, IPv4Address, IPv6Address"
        } else {
            "ip addr show"
        };
        
        self.run_in_terminal(command).await
    }
    
    /// Test network connectivity
    pub async fn test_connectivity(&self, host: &str) -> Result<ToolResult> {
        let command = if cfg!(target_os = "windows") {
            format!("Test-NetConnection {} -Port 80", host)
        } else {
            format!("ping -c 4 {}", host)
        };
        
        self.run_in_terminal(&command).await
    }
}
