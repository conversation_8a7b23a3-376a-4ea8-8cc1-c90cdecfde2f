//! UI components for X Terminal
//! 
//! This module implements the various UI components like blocks, input areas,
//! status bars, and other interface elements.

use crate::blocks::{BlockManager, Block, BlockStatus};
use crate::core::events::InputEvent;
use anyhow::Result;
use uuid::Uuid;

/// Component manager for handling UI components
pub struct ComponentManager {
    /// Block components
    block_components: Vec<BlockComponent>,
    /// Input component
    input_component: InputComponent,
    /// Status bar component
    status_bar: StatusBarComponent,
    /// Sidebar component
    sidebar: SidebarComponent,
}

/// Block component representing a command block in the UI
#[derive(Debug, Clone)]
pub struct BlockComponent {
    /// Block ID
    pub id: Uuid,
    /// Block data
    pub block: Block,
    /// Visual state
    pub visual_state: BlockVisualState,
    /// Position and size
    pub bounds: ComponentBounds,
}

/// Visual state of a block component
#[derive(Debug, Clone)]
pub struct BlockVisualState {
    /// Whether the block is expanded
    pub expanded: bool,
    /// Whether the block is selected
    pub selected: bool,
    /// Whether the block is highlighted
    pub highlighted: bool,
    /// Animation state
    pub animation_state: AnimationState,
    /// Whether the block is visible
    pub visible: bool,
    /// Whether the block is enabled
    pub enabled: bool,
    /// Z-index for layering
    pub z_index: i32,
}

impl Default for BlockVisualState {
    fn default() -> Self {
        Self {
            expanded: true,
            selected: false,
            highlighted: false,
            animation_state: AnimationState::Static,
            visible: true,
            enabled: true,
            z_index: 0,
        }
    }
}

/// Animation state for components
#[derive(Debug, Clone)]
pub enum AnimationState {
    /// No animation
    Static,
    /// Fade in animation
    FadeIn { progress: f32 },
    /// Fade out animation
    FadeOut { progress: f32 },
    /// Slide animation
    Slide { from: (f32, f32), to: (f32, f32), progress: f32 },
}

/// Component bounds (position and size)
#[derive(Debug, Clone)]
pub struct ComponentBounds {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

/// Render context for drawing components
#[derive(Debug)]
pub struct RenderContext {
    /// Current viewport size
    pub viewport_size: (f32, f32),
    /// Current time for animations
    pub time: f32,
    /// Delta time since last frame
    pub delta_time: f32,
}

/// Input component for command input
#[derive(Debug, Clone)]
pub struct InputComponent {
    /// Current input text
    pub text: String,
    /// Cursor position
    pub cursor_position: usize,
    /// Selection range
    pub selection: Option<(usize, usize)>,
    /// Input mode
    pub mode: InputMode,
    /// Bounds
    pub bounds: ComponentBounds,
}

/// Input modes
#[derive(Debug, Clone)]
pub enum InputMode {
    /// Normal command input
    Command,
    /// Search mode
    Search,
    /// AI prompt mode
    AIPrompt,
}

/// Status bar component
#[derive(Debug, Clone)]
pub struct StatusBarComponent {
    /// Current working directory
    pub working_dir: String,
    /// Shell information
    pub shell_info: String,
    /// AI status
    pub ai_status: String,
    /// Connection status
    pub connection_status: String,
    /// Bounds
    pub bounds: ComponentBounds,
}

/// Sidebar component
#[derive(Debug, Clone)]
pub struct SidebarComponent {
    /// Whether sidebar is visible
    pub visible: bool,
    /// Sidebar content
    pub content: SidebarContent,
    /// Bounds
    pub bounds: ComponentBounds,
}

/// Sidebar content types
#[derive(Debug, Clone)]
pub enum SidebarContent {
    /// Command history
    History,
    /// Bookmarked blocks
    Bookmarks,
    /// AI suggestions
    AISuggestions,
    /// File browser
    FileBrowser,
}

impl ComponentManager {
    /// Create a new component manager
    pub async fn new() -> Result<Self> {
        Ok(Self {
            block_components: Vec::new(),
            input_component: InputComponent {
                text: String::new(),
                cursor_position: 0,
                selection: None,
                mode: InputMode::Command,
                bounds: ComponentBounds {
                    x: 0.0,
                    y: 0.0,
                    width: 800.0,
                    height: 30.0,
                },
            },
            status_bar: StatusBarComponent {
                working_dir: std::env::current_dir()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string(),
                shell_info: "bash".to_string(),
                ai_status: "Ready".to_string(),
                connection_status: "Connected".to_string(),
                bounds: ComponentBounds {
                    x: 0.0,
                    y: 570.0,
                    width: 800.0,
                    height: 30.0,
                },
            },
            sidebar: SidebarComponent {
                visible: false,
                content: SidebarContent::History,
                bounds: ComponentBounds {
                    x: 600.0,
                    y: 0.0,
                    width: 200.0,
                    height: 600.0,
                },
            },
        })
    }
    
    /// Update block components based on block manager state
    pub async fn update_blocks(&mut self, block_manager: &BlockManager) -> Result<()> {
        // Clear existing block components
        self.block_components.clear();
        
        // Create components for all blocks
        let blocks = block_manager.get_all_blocks();
        let mut y_offset = 40.0; // Start below input area
        
        for block in blocks {
            let visual_state = BlockVisualState {
                expanded: true,
                selected: false,
                highlighted: block.status == BlockStatus::Running,
                animation_state: AnimationState::Static,
                visible: true,
                enabled: true,
                z_index: 0,
            };
            
            // Calculate block height based on content
            let content_lines = block.output.lines().count().max(1);
            let block_height = 60.0 + (content_lines as f32 * 16.0); // Header + content
            
            let bounds = ComponentBounds {
                x: 10.0,
                y: y_offset,
                width: 780.0,
                height: block_height,
            };
            
            let component = BlockComponent {
                id: block.id,
                block: block.clone(),
                visual_state,
                bounds,
            };
            
            self.block_components.push(component);
            y_offset += block_height + 10.0; // Add spacing between blocks
        }
        
        Ok(())
    }
    
    /// Get block component by ID
    pub fn get_block_component(&self, id: Uuid) -> Option<&BlockComponent> {
        self.block_components.iter().find(|c| c.id == id)
    }
    
    /// Get mutable block component by ID
    pub fn get_block_component_mut(&mut self, id: Uuid) -> Option<&mut BlockComponent> {
        self.block_components.iter_mut().find(|c| c.id == id)
    }
    
    /// Update input component
    pub fn update_input(&mut self, text: String, cursor_position: usize) {
        self.input_component.text = text;
        self.input_component.cursor_position = cursor_position;
    }
    
    /// Update status bar
    pub fn update_status_bar(&mut self, working_dir: String, shell_info: String) {
        self.status_bar.working_dir = working_dir;
        self.status_bar.shell_info = shell_info;
    }
    
    /// Toggle sidebar visibility
    pub fn toggle_sidebar(&mut self) {
        self.sidebar.visible = !self.sidebar.visible;
    }
    
    /// Set sidebar content
    pub fn set_sidebar_content(&mut self, content: SidebarContent) {
        self.sidebar.content = content;
    }
    
    /// Get all visible components for rendering
    pub fn get_visible_components(&self) -> Vec<&dyn Component> {
        let mut components: Vec<&dyn Component> = Vec::new();
        
        // Add block components
        for block_component in &self.block_components {
            components.push(block_component);
        }
        
        // Add input component
        components.push(&self.input_component);
        
        // Add status bar
        components.push(&self.status_bar);
        
        // Add sidebar if visible
        if self.sidebar.visible {
            components.push(&self.sidebar);
        }
        
        components
    }
}

/// Trait for UI components
pub trait Component {
    /// Get component bounds
    fn bounds(&self) -> &ComponentBounds;

    /// Check if point is inside component
    fn contains_point(&self, x: f32, y: f32) -> bool {
        let bounds = self.bounds();
        x >= bounds.x && x <= bounds.x + bounds.width &&
        y >= bounds.y && y <= bounds.y + bounds.height
    }

    /// Get component type name
    fn component_type(&self) -> &'static str;

    /// Check if component is visible
    fn is_visible(&self) -> bool { true }

    /// Set component visibility
    fn set_visible(&mut self, _visible: bool) {}

    /// Check if component is enabled
    fn is_enabled(&self) -> bool { true }

    /// Set component enabled state
    fn set_enabled(&mut self, _enabled: bool) {}

    /// Get component z-index for layering
    fn z_index(&self) -> i32 { 0 }

    /// Set component z-index
    fn set_z_index(&mut self, _z_index: i32) {}

    /// Update component (called each frame)
    fn update(&mut self, _delta_time: f32) -> Result<()> { Ok(()) }

    /// Render the component
    fn render(&self, _ctx: &mut RenderContext) -> Result<()> { Ok(()) }

    /// Handle input event
    fn handle_input(&mut self, _event: &InputEvent) -> Result<bool> { Ok(false) }
}

impl Component for BlockComponent {
    fn bounds(&self) -> &ComponentBounds {
        &self.bounds
    }
    
    fn component_type(&self) -> &'static str {
        "block"
    }
}

impl Component for InputComponent {
    fn bounds(&self) -> &ComponentBounds {
        &self.bounds
    }
    
    fn component_type(&self) -> &'static str {
        "input"
    }
}

impl Component for StatusBarComponent {
    fn bounds(&self) -> &ComponentBounds {
        &self.bounds
    }
    
    fn component_type(&self) -> &'static str {
        "status_bar"
    }
}

impl Component for SidebarComponent {
    fn bounds(&self) -> &ComponentBounds {
        &self.bounds
    }
    
    fn component_type(&self) -> &'static str {
        "sidebar"
    }
}
