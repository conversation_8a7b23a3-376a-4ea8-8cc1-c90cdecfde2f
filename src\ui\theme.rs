//! Theme system for X Terminal UI
//! 
//! This module defines the theming system for colors, fonts, and visual styles.

use serde::{Deserialize, Serialize};

/// UI theme configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UITheme {
    /// Theme name
    pub name: String,
    /// Color scheme
    pub colors: ColorScheme,
    /// Typography settings
    pub typography: Typography,
    /// Spacing and sizing
    pub spacing: Spacing,
    /// Animation settings
    pub animations: Animations,
}

/// Color scheme for the UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorScheme {
    /// Background colors
    pub background: BackgroundColors,
    /// Foreground colors
    pub foreground: ForegroundColors,
    /// Accent colors
    pub accent: AccentColors,
    /// Status colors
    pub status: StatusColors,
    /// Terminal colors
    pub terminal: TerminalColors,
}

/// Background colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackgroundColors {
    pub primary: Color,
    pub secondary: Color,
    pub tertiary: Color,
    pub surface: Color,
    pub overlay: Color,
}

/// Foreground colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ForegroundColors {
    pub primary: Color,
    pub secondary: Color,
    pub tertiary: Color,
    pub disabled: Color,
}

/// Accent colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccentColors {
    pub primary: Color,
    pub secondary: Color,
    pub highlight: Color,
    pub selection: Color,
}

/// Status colors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusColors {
    pub success: Color,
    pub warning: Color,
    pub error: Color,
    pub info: Color,
}

/// Terminal colors (16-color palette)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalColors {
    pub black: Color,
    pub red: Color,
    pub green: Color,
    pub yellow: Color,
    pub blue: Color,
    pub magenta: Color,
    pub cyan: Color,
    pub white: Color,
    pub bright_black: Color,
    pub bright_red: Color,
    pub bright_green: Color,
    pub bright_yellow: Color,
    pub bright_blue: Color,
    pub bright_magenta: Color,
    pub bright_cyan: Color,
    pub bright_white: Color,
}

/// Color representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Color {
    pub r: f32,
    pub g: f32,
    pub b: f32,
    pub a: f32,
}

impl Color {
    /// Create a new color from RGB values (0-255)
    pub fn rgb(r: u8, g: u8, b: u8) -> Self {
        Self {
            r: r as f32 / 255.0,
            g: g as f32 / 255.0,
            b: b as f32 / 255.0,
            a: 1.0,
        }
    }
    
    /// Create a new color from RGBA values (0-255)
    pub fn rgba(r: u8, g: u8, b: u8, a: u8) -> Self {
        Self {
            r: r as f32 / 255.0,
            g: g as f32 / 255.0,
            b: b as f32 / 255.0,
            a: a as f32 / 255.0,
        }
    }
    
    /// Create a new color from hex string
    pub fn hex(hex: &str) -> Result<Self, String> {
        let hex = hex.trim_start_matches('#');
        if hex.len() != 6 && hex.len() != 8 {
            return Err("Invalid hex color format".to_string());
        }
        
        let r = u8::from_str_radix(&hex[0..2], 16).map_err(|_| "Invalid hex color")?;
        let g = u8::from_str_radix(&hex[2..4], 16).map_err(|_| "Invalid hex color")?;
        let b = u8::from_str_radix(&hex[4..6], 16).map_err(|_| "Invalid hex color")?;
        let a = if hex.len() == 8 {
            u8::from_str_radix(&hex[6..8], 16).map_err(|_| "Invalid hex color")?
        } else {
            255
        };
        
        Ok(Self::rgba(r, g, b, a))
    }
}

/// Typography settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Typography {
    pub font_family: String,
    pub font_size: f32,
    pub line_height: f32,
    pub letter_spacing: f32,
    pub font_weight: FontWeight,
}

/// Font weight
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FontWeight {
    Thin,
    Light,
    Normal,
    Medium,
    Bold,
    Black,
}

/// Spacing and sizing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Spacing {
    pub xs: f32,
    pub sm: f32,
    pub md: f32,
    pub lg: f32,
    pub xl: f32,
    pub border_radius: f32,
    pub border_width: f32,
}

/// Animation settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Animations {
    pub duration_fast: f32,
    pub duration_normal: f32,
    pub duration_slow: f32,
    pub easing: EasingFunction,
}

/// Easing functions for animations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    Bounce,
}

impl Default for UITheme {
    fn default() -> Self {
        Self {
            name: "Default Dark".to_string(),
            colors: ColorScheme {
                background: BackgroundColors {
                    primary: Color::rgb(18, 18, 18),
                    secondary: Color::rgb(28, 28, 28),
                    tertiary: Color::rgb(38, 38, 38),
                    surface: Color::rgb(48, 48, 48),
                    overlay: Color::rgba(0, 0, 0, 128),
                },
                foreground: ForegroundColors {
                    primary: Color::rgb(255, 255, 255),
                    secondary: Color::rgb(200, 200, 200),
                    tertiary: Color::rgb(150, 150, 150),
                    disabled: Color::rgb(100, 100, 100),
                },
                accent: AccentColors {
                    primary: Color::rgb(0, 122, 255),
                    secondary: Color::rgb(88, 166, 255),
                    highlight: Color::rgb(255, 204, 0),
                    selection: Color::rgba(0, 122, 255, 64),
                },
                status: StatusColors {
                    success: Color::rgb(52, 199, 89),
                    warning: Color::rgb(255, 149, 0),
                    error: Color::rgb(255, 59, 48),
                    info: Color::rgb(90, 200, 250),
                },
                terminal: TerminalColors {
                    black: Color::rgb(0, 0, 0),
                    red: Color::rgb(255, 59, 48),
                    green: Color::rgb(52, 199, 89),
                    yellow: Color::rgb(255, 204, 0),
                    blue: Color::rgb(0, 122, 255),
                    magenta: Color::rgb(255, 45, 85),
                    cyan: Color::rgb(100, 210, 255),
                    white: Color::rgb(255, 255, 255),
                    bright_black: Color::rgb(128, 128, 128),
                    bright_red: Color::rgb(255, 105, 97),
                    bright_green: Color::rgb(99, 230, 190),
                    bright_yellow: Color::rgb(255, 212, 38),
                    bright_blue: Color::rgb(64, 156, 255),
                    bright_magenta: Color::rgb(255, 100, 130),
                    bright_cyan: Color::rgb(154, 236, 254),
                    bright_white: Color::rgb(255, 255, 255),
                },
            },
            typography: Typography {
                font_family: "JetBrains Mono".to_string(),
                font_size: 14.0,
                line_height: 1.4,
                letter_spacing: 0.0,
                font_weight: FontWeight::Normal,
            },
            spacing: Spacing {
                xs: 4.0,
                sm: 8.0,
                md: 16.0,
                lg: 24.0,
                xl: 32.0,
                border_radius: 6.0,
                border_width: 1.0,
            },
            animations: Animations {
                duration_fast: 0.15,
                duration_normal: 0.25,
                duration_slow: 0.35,
                easing: EasingFunction::EaseInOut,
            },
        }
    }
}

impl UITheme {
    /// Create a light theme variant
    pub fn light() -> Self {
        let mut theme = Self::default();
        theme.name = "Default Light".to_string();
        
        // Swap background and foreground colors
        theme.colors.background = BackgroundColors {
            primary: Color::rgb(255, 255, 255),
            secondary: Color::rgb(248, 248, 248),
            tertiary: Color::rgb(240, 240, 240),
            surface: Color::rgb(230, 230, 230),
            overlay: Color::rgba(255, 255, 255, 200),
        };
        
        theme.colors.foreground = ForegroundColors {
            primary: Color::rgb(0, 0, 0),
            secondary: Color::rgb(60, 60, 60),
            tertiary: Color::rgb(120, 120, 120),
            disabled: Color::rgb(180, 180, 180),
        };
        
        theme
    }
}
